package hash

import (
	"unsafe"
)

type TEB struct {
	verbose bool
}

func NewTEB(verbose bool) *TEB {
	return &TEB{
		verbose: verbose,
	}
}

var TEBInstance = NewTEB(true)

// 获取TEB地址 - 真正的实现
// 在栈附近搜索TEB
func (t *TEB) findTEBNearStack(stackAddr uintptr) uintptr {
	// TEB的特征：开头通常是一些固定的结构
	// 我们搜索可能的TEB位置

	// 对齐到页边界
	pageSize := uintptr(0x1000)
	startAddr := (stackAddr &^ (pageSize - 1)) - pageSize*16 // 向前搜索16页
	endAddr := (stackAddr &^ (pageSize - 1)) + pageSize*16   // 向后搜索16页

	for addr := startAddr; addr < endAddr; addr += pageSize {
		if teb := t.validateTEB(addr); teb != 0 {
			// 从TEB获取PEB
			if unsafe.Sizeof(uintptr(0)) == 8 {
				return *(*uintptr)(unsafe.Pointer(teb + 0x60)) // x64: PEB在偏移0x60
			} else {
				return *(*uintptr)(unsafe.Pointer(teb + 0x30)) // x86: PEB在偏移0x30
			}
		}
	}

	return 0
}

// 验证是否是有效的TEB - 使用直接调用避免递归
func (t *TEB) validateTEB(addr uintptr) uintptr {
	// 安全检查：确保地址可读 - 使用直接调用避免递归
	if !CheckerInstance.IsAddressReadableWithDirectCall(addr) {
		return 0
	}

	// TEB的一些特征验证
	// 1. 检查SEH链指针是否合理
	sehChain := *(*uintptr)(unsafe.Pointer(addr))
	if sehChain == 0 || sehChain == 0xFFFFFFFF {
		return 0
	}

	// 2. 检查栈基址和栈顶是否合理
	stackBase := *(*uintptr)(unsafe.Pointer(addr + 0x08))
	stackLimit := *(*uintptr)(unsafe.Pointer(addr + 0x10))

	if stackBase <= stackLimit || stackBase-stackLimit > 0x100000 { // 栈大小不应超过1MB
		return 0
	}

	// 3. 检查PEB指针是否合理
	var pebOffset uintptr
	if unsafe.Sizeof(uintptr(0)) == 8 {
		pebOffset = 0x60
	} else {
		pebOffset = 0x30
	}

	peb := *(*uintptr)(unsafe.Pointer(addr + pebOffset))
	if peb == 0 || !CheckerInstance.IsAddressReadableWithDirectCall(peb) { // 使用直接调用
		return 0
	}

	// 验证PEB的一些特征
	if PEBInstance.ValidatePEBWithDirectCall(peb) { // 使用直接调用版本
		return addr
	}

	return 0
}
