package hash

import (
	"etwloader/pe"
	"fmt"
	"unsafe"
)

// 通过哈希查找函数
func (ah *APIHasher) GetFunctionByHash(dllBase, exportDir uintptr, targetHash uint32) (uintptr, error) {
	return ah.getFunctionByHashWithLearning(dllBase, exportDir, targetHash)
}

// 通过哈希查找函数（带学习功能）
func (ah *APIHasher) getFunctionByHashWithLearning(dllBase, exportDir uintptr, targetHash uint32) (uintptr, error) {
	export := (*pe.IMAGE_EXPORT_DIRECTORY)(unsafe.Pointer(exportDir))

	namesRVA := export.AddressOfNames
	functionsRVA := export.AddressOfFunctions
	ordinalsRVA := export.AddressOfNameOrdinals

	// 获取导出表数组（动态分配大小）
	maxFunctions := export.NumberOfNames
	if maxFunctions > export.NumberOfFunctions {
		maxFunctions = export.NumberOfFunctions
	}
	if maxFunctions == 0 {
		return 0, fmt.Errorf("no functions in export table")
	}

	// 安全地创建切片
	names := (*[65536]uint32)(unsafe.Pointer(dllBase + uintptr(namesRVA)))[:maxFunctions:maxFunctions]
	functions := (*[65536]uint32)(unsafe.Pointer(dllBase + uintptr(functionsRVA)))[:export.NumberOfFunctions:export.NumberOfFunctions]
	ordinals := (*[65536]uint16)(unsafe.Pointer(dllBase + uintptr(ordinalsRVA)))[:maxFunctions:maxFunctions]

	for i := uint32(0); i < export.NumberOfNames; i++ {
		nameRVA := names[i]
		namePtr := dllBase + uintptr(nameRVA)
		name := ah.cStringToGo(namePtr)

		// 自动学习：记录所有发现的函数哈希
		if ah.autoUpdateHashes {
			funcHash := ah.CalculateHash(name)
			if _, exists := ah.dynamicHashes[funcHash]; !exists {
				ah.dynamicHashes[funcHash] = name
				if ah.verbose && funcHash == targetHash {
					ah.logf("📚 Learned new API hash: 0x%08x -> %s", funcHash, name)
				}
			}
		}

		if ah.CalculateHash(name) == targetHash {
			ordinal := ordinals[i]
			functionRVA := functions[ordinal]
			return dllBase + uintptr(functionRVA), nil
		}
	}

	return 0, fmt.Errorf("function not found")
}
