package main

import (
	"fmt"
	"syscall"
	"unsafe"

	"golang.org/x/sys/windows"
)

func main() {
	fmt.Println("🔍 分析当前进程的内存布局...")
	
	process := windows.CurrentProcess()
	var mbi windows.MemoryBasicInformation
	var address uintptr = 0x10000
	maxAddress := uintptr(0x7FFFFFFF)
	
	executableRegions := []struct {
		Base uintptr
		Size uintptr
		Protection uint32
	}{}
	
	totalExecutableSize := uintptr(0)
	regionCount := 0
	
	for address < maxAddress && regionCount < 1000 {
		ret, _, _ := syscall.Syscall6(
			syscall.NewLazyDLL("kernel32.dll").NewProc("VirtualQueryEx").Addr(),
			4,
			uintptr(process),
			address,
			uintptr(unsafe.Pointer(&mbi)),
			unsafe.Sizeof(mbi),
			0, 0,
		)
		
		if ret == 0 {
			address += 0x1000
			regionCount++
			continue
		}
		
		regionCount++
		
		// 检查是否是可执行区域
		if mbi.State == windows.MEM_COMMIT &&
			(mbi.Protect&windows.PAGE_EXECUTE != 0 ||
				mbi.Protect&windows.PAGE_EXECUTE_READ != 0 ||
				mbi.Protect&windows.PAGE_EXECUTE_READWRITE != 0 ||
				mbi.Protect&windows.PAGE_EXECUTE_WRITECOPY != 0) {
			
			executableRegions = append(executableRegions, struct {
				Base uintptr
				Size uintptr
				Protection uint32
			}{
				Base: mbi.BaseAddress,
				Size: mbi.RegionSize,
				Protection: mbi.Protect,
			})
			
			totalExecutableSize += mbi.RegionSize
			
			fmt.Printf("📍 可执行区域: Base=0x%08x, Size=%8d bytes (%6.1f KB), Protect=0x%02x\n",
				mbi.BaseAddress, mbi.RegionSize, float64(mbi.RegionSize)/1024, mbi.Protect)
		}
		
		if mbi.RegionSize > 0 {
			address = mbi.BaseAddress + mbi.RegionSize
		} else {
			address += 0x1000
		}
	}
	
	fmt.Printf("\n📊 统计结果:\n")
	fmt.Printf("   总可执行区域数量: %d\n", len(executableRegions))
	fmt.Printf("   总可执行内存大小: %d bytes (%.1f KB, %.1f MB)\n", 
		totalExecutableSize, float64(totalExecutableSize)/1024, float64(totalExecutableSize)/(1024*1024))
	
	// 分析不同大小的shellcode能找到多少合适区域
	testSizes := []int{1024, 4096, 16384, 65536, 262144, 1048576, 2570731} // 1KB, 4KB, 16KB, 64KB, 256KB, 1MB, calc.bin大小
	
	fmt.Printf("\n🎯 不同shellcode大小的适用性分析:\n")
	for _, size := range testSizes {
		suitableCount := 0
		for _, region := range executableRegions {
			if region.Size >= uintptr(size) {
				suitableCount++
			}
		}
		fmt.Printf("   %8d bytes (%6.1f KB): %d个合适区域\n", 
			size, float64(size)/1024, suitableCount)
	}
}
