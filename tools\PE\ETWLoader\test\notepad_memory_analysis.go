package main

import (
	"fmt"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

func main() {
	fmt.Println("🔍 分析notepad.exe的内存布局...")
	
	// 启动notepad.exe
	fmt.Println("📝 启动notepad.exe...")

	// 使用CreateProcess启动notepad
	var si windows.StartupInfo
	var pi windows.ProcessInformation
	si.Cb = uint32(unsafe.Sizeof(si))

	commandLine, _ := windows.UTF16PtrFromString("notepad.exe")

	err := windows.CreateProcess(
		nil,        // lpApplicationName
		commandLine, // lpCommandLine
		nil,        // lpProcessAttributes
		nil,        // lpThreadAttributes
		false,      // bInheritHandles
		0,          // dwCreationFlags
		nil,        // lpEnvironment
		nil,        // lpCurrentDirectory
		&si,        // lpStartupInfo
		&pi,        // lpProcessInformation
	)

	if err != nil {
		fmt.Printf("❌ 启动notepad失败: %v\n", err)
		return
	}

	fmt.Printf("✅ notepad.exe启动成功，PID: %d\n", pi.ProcessId)

	// 等待一下让notepad完全启动
	time.Sleep(1 * time.Second)

	// 打开进程
	process, err := windows.OpenProcess(windows.PROCESS_QUERY_INFORMATION|windows.PROCESS_VM_READ, false, pi.ProcessId)
	if err != nil {
		fmt.Printf("❌ 打开进程失败: %v\n", err)
		return
	}
	defer windows.CloseHandle(process)
	
	var mbi windows.MemoryBasicInformation
	var address uintptr = 0x10000
	maxAddress := uintptr(0x7FFFFFFF)
	
	executableRegions := []struct {
		Base uintptr
		Size uintptr
		Protection uint32
	}{}
	
	totalExecutableSize := uintptr(0)
	regionCount := 0
	
	fmt.Println("\n📍 notepad.exe的可执行内存区域:")
	
	for address < maxAddress && regionCount < 1000 {
		ret, _, _ := syscall.Syscall6(
			syscall.NewLazyDLL("kernel32.dll").NewProc("VirtualQueryEx").Addr(),
			4,
			uintptr(process),
			address,
			uintptr(unsafe.Pointer(&mbi)),
			unsafe.Sizeof(mbi),
			0, 0,
		)
		
		if ret == 0 {
			address += 0x1000
			regionCount++
			continue
		}
		
		regionCount++
		
		// 检查是否是可执行区域
		if mbi.State == windows.MEM_COMMIT &&
			(mbi.Protect&windows.PAGE_EXECUTE != 0 ||
				mbi.Protect&windows.PAGE_EXECUTE_READ != 0 ||
				mbi.Protect&windows.PAGE_EXECUTE_READWRITE != 0 ||
				mbi.Protect&windows.PAGE_EXECUTE_WRITECOPY != 0) {
			
			executableRegions = append(executableRegions, struct {
				Base uintptr
				Size uintptr
				Protection uint32
			}{
				Base: mbi.BaseAddress,
				Size: mbi.RegionSize,
				Protection: mbi.Protect,
			})
			
			totalExecutableSize += mbi.RegionSize
			
			fmt.Printf("   Base=0x%08x, Size=%8d bytes (%6.1f KB), Protect=0x%02x\n",
				mbi.BaseAddress, mbi.RegionSize, float64(mbi.RegionSize)/1024, mbi.Protect)
		}
		
		if mbi.RegionSize > 0 {
			address = mbi.BaseAddress + mbi.RegionSize
		} else {
			address += 0x1000
		}
	}
	
	fmt.Printf("\n📊 notepad.exe统计结果:\n")
	fmt.Printf("   总可执行区域数量: %d\n", len(executableRegions))
	fmt.Printf("   总可执行内存大小: %d bytes (%.1f KB, %.1f MB)\n", 
		totalExecutableSize, float64(totalExecutableSize)/1024, float64(totalExecutableSize)/(1024*1024))
	
	// 分析不同大小的shellcode能找到多少合适区域
	testSizes := []int{1024, 4096, 16384, 65536, 262144, 1048576, 2570731}
	
	fmt.Printf("\n🎯 notepad.exe中不同shellcode大小的适用性:\n")
	for _, size := range testSizes {
		suitableCount := 0
		for _, region := range executableRegions {
			if region.Size >= uintptr(size) {
				suitableCount++
			}
		}
		fmt.Printf("   %8d bytes (%6.1f KB): %d个合适区域\n", 
			size, float64(size)/1024, suitableCount)
	}
	
	// 终止notepad进程
	fmt.Println("\n🔚 终止notepad.exe...")
	windows.TerminateProcess(process, 0)
	windows.CloseHandle(pi.Process)
	windows.CloseHandle(pi.Thread)
}
