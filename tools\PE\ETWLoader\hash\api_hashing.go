package hash

import (
	"fmt"
	"golang.org/x/sys/windows"
	"strings"
	"syscall"
	"unsafe"
)

// API哈希调用模块
type APIHasher struct {
	verbose          bool
	silent           bool              // 静默模式
	dynamicHashes    map[uint32]string // 动态发现的哈希值
	hashMisses       int               // 哈希未命中次数
	hashHits         int               // 哈希命中次数
	autoUpdateHashes bool              // 是否自动更新哈希表
}

// 创建新的API哈希器
func NewAPIHasher(verbose bool) *APIHasher {
	return &APIHasher{
		verbose:          verbose,
		dynamicHashes:    make(map[uint32]string),
		autoUpdateHashes: true,
	}
}

var APIHasherInstance = NewAPIHasher(true)

// 获取Ldr数据
func (ah *APIHasher) getLdrData(peb uintptr) uintptr {
	return *(*uintptr)(unsafe.Pointer(peb + 0x18))
}

// 获取模块列表
func (ah *APIHasher) getModuleList(ldr uintptr) uintptr {
	return ldr + 0x10 // InLoadOrderModuleList
}

// 获取模块条目
func (ah *APIHasher) getModuleEntry(listEntry uintptr) uintptr {
	return listEntry
}

// 获取模块名称
func (ah *APIHasher) getModuleName(module uintptr) string {
	// LDR_DATA_TABLE_ENTRY结构中BaseDllName的偏移
	// BaseDllName是UNICODE_STRING结构，偏移0x58
	baseDllNamePtr := module + 0x58

	// UNICODE_STRING结构: Length(2) + MaximumLength(2) + Buffer(8)
	nameLen := *(*uint16)(unsafe.Pointer(baseDllNamePtr))
	namePtr := *(*uintptr)(unsafe.Pointer(baseDllNamePtr + 8))

	if namePtr == 0 || nameLen == 0 {
		return ""
	}

	// 转换Unicode字符串为Go字符串
	name16 := (*[256]uint16)(unsafe.Pointer(namePtr))[:nameLen/2]
	return syscall.UTF16ToString(name16)
}

// 获取模块基址
func (ah *APIHasher) getModuleBase(module uintptr) uintptr {
	// DllBase在LDR_DATA_TABLE_ENTRY中的偏移是0x30
	return *(*uintptr)(unsafe.Pointer(module + 0x30))
}

// 获取下一个模块
func (ah *APIHasher) getNextModule(current uintptr) uintptr {
	return *(*uintptr)(unsafe.Pointer(current))
}

// 计算字符串哈希
func (ah *APIHasher) CalculateHash(s string) uint32 {
	// 转换为小写以确保一致性
	s = strings.ToLower(s)

	// 使用简单的哈希算法（实际可以使用更复杂的）
	hash := uint32(0)
	for _, c := range []byte(s) {
		hash = hash*31 + uint32(c)
	}
	return hash
}

// C字符串转Go字符串
func (ah *APIHasher) cStringToGo(ptr uintptr) string {
	var result []byte
	for i := 0; ; i++ {
		b := *(*byte)(unsafe.Pointer(ptr + uintptr(i)))
		if b == 0 {
			break
		}
		result = append(result, b)
	}
	return string(result)
}

// 便捷的API调用函数 - 多层保障机制
func (ah *APIHasher) CallAPI(dllName, apiName string, args ...uintptr) (uintptr, error) {
	// 计算哈希
	dllHash := ah.CalculateHash(dllName)
	apiHash := ah.CalculateHash(apiName)

	ah.logf("Calling %s!%s (DLL:0x%08x, API:0x%08x)", dllName, apiName, dllHash, apiHash)

	// 第一层：尝试哈希方式调用
	apiAddr, err := ah.GetAPIByHash(dllHash, apiHash)
	if err == nil && apiAddr != 0 {
		ah.logf("✅ Found API via hash at address: 0x%x", apiAddr)

		// 尝试调用
		result, callErr := DirectSysCall.callAPIByAddress(apiAddr, args...)
		if callErr == nil {
			ah.logf("✅ Hash-based call succeeded: 0x%x", result)
			return result, nil
		}

		ah.logf("⚠️ Hash-based call failed: %v, trying fallback", callErr)
	} else {
		ah.logf("⚠️ Hash resolution failed: %v, trying fallback", err)
	}

	// 第二层：回退到传统方式
	ah.logf("🔄 Falling back to traditional API call")
	result, err := ah.callAPITraditional(dllName, apiName, args...)
	if err == nil {
		ah.logf("✅ Traditional call succeeded: 0x%x", result)
		return result, nil
	}

	ah.logf("❌ Traditional call also failed: %v", err)

	// 第三层：尝试动态加载（最后的保障）
	ah.logf("🔄 Trying dynamic loading as last resort")
	return ah.callAPIWithDynamicLoading(dllName, apiName, args...)
}

// 直接通过预计算哈希调用API（更高效）
func (ah *APIHasher) CallAPIByHash(dllHash, apiHash uint32, args ...uintptr) (uintptr, error) {
	// 检查哈希表
	dllName, dllExists := dllHashes[dllHash]
	apiName, apiExists := apiHashes[apiHash]

	if !dllExists || !apiExists {
		return 0, fmt.Errorf("unknown hash - DLL:0x%08x, API:0x%08x", dllHash, apiHash)
	}

	ah.logf("Direct hash call: %s!%s", dllName, apiName)

	// 获取API地址
	apiAddr, err := ah.GetAPIByHash(dllHash, apiHash)
	if err != nil {
		return 0, err
	}

	// 调用API
	return DirectSysCall.callAPIByAddress(apiAddr, args...)
}

// 传统API调用（作为后备）
func (ah *APIHasher) callAPITraditional(dllName, apiName string, args ...uintptr) (uintptr, error) {
	dll := windows.NewLazySystemDLL(dllName)
	proc := dll.NewProc(apiName)

	ret, _, err := proc.Call(args...)
	if err != nil && err.Error() != "The operation completed successfully." {
		return ret, err
	}
	return ret, nil
}

// 动态加载API调用（最后的保障）
func (ah *APIHasher) callAPIWithDynamicLoading(dllName, apiName string, args ...uintptr) (uintptr, error) {
	ah.logf("🔧 Attempting dynamic loading for %s!%s", dllName, apiName)

	// 首先尝试加载DLL
	dllHandle, err := ah.loadLibrary(dllName)
	if err != nil {
		return 0, fmt.Errorf("failed to load library %s: %v", dllName, err)
	}

	// 获取函数地址
	procAddr, err := ah.getProcAddress(dllHandle, apiName)
	if err != nil {
		return 0, fmt.Errorf("failed to get proc address for %s: %v", apiName, err)
	}

	ah.logf("✅ Dynamic loading found %s at: 0x%x", apiName, procAddr)

	// 调用函数
	return DirectSysCall.callAPIByAddress(procAddr, args...)
}

// 动态加载库
func (ah *APIHasher) loadLibrary(dllName string) (uintptr, error) {
	// 尝试使用已经加载的kernel32.dll中的LoadLibraryA
	kernel32Hash := ah.CalculateHash("kernel32.dll")
	loadLibraryHash := ah.CalculateHash("LoadLibraryA")

	loadLibraryAddr, err := ah.GetAPIByHash(kernel32Hash, loadLibraryHash)
	if err != nil {
		// 如果哈希方式失败，使用传统方式
		dll := windows.NewLazySystemDLL("kernel32.dll")
		proc := dll.NewProc("LoadLibraryA")

		dllNamePtr, _ := syscall.BytePtrFromString(dllName)
		ret, _, err := proc.Call(uintptr(unsafe.Pointer(dllNamePtr)))
		if err != nil && err.Error() != "The operation completed successfully." {
			return 0, err
		}
		return ret, nil
	}

	// 使用哈希方式调用LoadLibraryA
	dllNamePtr, _ := syscall.BytePtrFromString(dllName)
	return DirectSysCall.callAPIByAddress(loadLibraryAddr, uintptr(unsafe.Pointer(dllNamePtr)))
}

// 获取函数地址
func (ah *APIHasher) getProcAddress(dllHandle uintptr, procName string) (uintptr, error) {
	// 尝试使用已经加载的kernel32.dll中的GetProcAddress
	kernel32Hash := ah.CalculateHash("kernel32.dll")
	getProcAddressHash := ah.CalculateHash("GetProcAddress")

	getProcAddressAddr, err := ah.GetAPIByHash(kernel32Hash, getProcAddressHash)
	if err != nil {
		// 如果哈希方式失败，使用传统方式
		dll := windows.NewLazySystemDLL("kernel32.dll")
		proc := dll.NewProc("GetProcAddress")

		procNamePtr, _ := syscall.BytePtrFromString(procName)
		ret, _, err := proc.Call(dllHandle, uintptr(unsafe.Pointer(procNamePtr)))
		if err != nil && err.Error() != "The operation completed successfully." {
			return 0, err
		}
		return ret, nil
	}

	// 使用哈希方式调用GetProcAddress
	procNamePtr, _ := syscall.BytePtrFromString(procName)
	return DirectSysCall.callAPIByAddress(getProcAddressAddr, dllHandle, uintptr(unsafe.Pointer(procNamePtr)))
}

// 获取哈希统计信息
func (ah *APIHasher) GetHashStats() (hits, misses int, dynamicCount int) {
	return ah.hashHits, ah.hashMisses, len(ah.dynamicHashes)
}

// 打印哈希统计信息
func (ah *APIHasher) PrintHashStats() {
	totalAttempts := ah.hashHits + ah.hashMisses
	hitRate := float64(0)
	if totalAttempts > 0 {
		hitRate = float64(ah.hashHits) / float64(totalAttempts) * 100
	}

	ah.logf("📊 Hash Statistics:")
	ah.logf("   Hits: %d, Misses: %d, Total: %d", ah.hashHits, ah.hashMisses, totalAttempts)
	ah.logf("   Hit Rate: %.1f%%", hitRate)
	ah.logf("   Dynamic Hashes Learned: %d", len(ah.dynamicHashes))

	if len(ah.dynamicHashes) > 0 && ah.verbose {
		ah.logf("📚 Learned Hashes:")
		for hash, name := range ah.dynamicHashes {
			ah.logf("   0x%08x -> %s", hash, name)
		}
	}
}

// 导出学习到的哈希值（用于更新预计算表）
func (ah *APIHasher) ExportLearnedHashes() map[uint32]string {
	exported := make(map[uint32]string)
	for hash, name := range ah.dynamicHashes {
		exported[hash] = name
	}
	return exported
}

// 重置统计信息
func (ah *APIHasher) ResetStats() {
	ah.hashHits = 0
	ah.hashMisses = 0
	ah.dynamicHashes = make(map[uint32]string)
}

// 设置自动更新哈希表
func (ah *APIHasher) SetAutoUpdateHashes(enable bool) {
	ah.autoUpdateHashes = enable
	ah.logf("🔧 Auto-update hashes: %v", enable)
}

func (ah *APIHasher) logf(format string, args ...interface{}) {
	if ah.verbose && !ah.silent {
		fmt.Printf("[API_HASH] "+format+"\n", args...)
	}
}

// 设置静默模式
func (ah *APIHasher) SetSilent(silent bool) {
	ah.silent = silent
}
