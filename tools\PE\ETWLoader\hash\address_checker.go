package hash

import (
	"golang.org/x/sys/windows"
	"unsafe"
)

type Checker struct{}

var CheckerInstance = Checker{}

// 使用哈希调用检查地址是否可读（主要方法）
func (ck *Checker) isAddressReadableWithHash(addr uintptr) bool {
	var mbi windows.MemoryBasicInformation

	ret, err := APIHasherInstance.CallAPI("kernel32.dll", "VirtualQuery",
		addr,                          // lpAddress
		uintptr(unsafe.Pointer(&mbi)), // lpBuffer
		unsafe.Sizeof(mbi),            // dwLength
	)

	if err != nil || ret == 0 {
		// 如果哈希调用失败，回退到直接调用
		return ck.IsAddressReadableWithDirectCall(addr)
	}

	// 检查内存是否已提交且可读
	return (mbi.State == 0x1000) && // MEM_COMMIT
		(mbi.Protect&0x01 == 0) && // 不是PAGE_NOACCESS
		(mbi.Protect&0x100 == 0) // 不是PAGE_GUARD
}

// 检查地址是否可读 - 带直接调用保障
func (ck *Checker) IsAddressReadable(addr uintptr) bool {
	// 为了避免无限递归，VirtualQuery使用直接调用作为保障
	return ck.IsAddressReadableWithDirectCall(addr)
}

// 使用直接调用检查地址是否可读（保障机制）
func (ck *Checker) IsAddressReadableWithDirectCall(addr uintptr) bool {
	var mbi windows.MemoryBasicInformation

	// 直接调用VirtualQuery，避免递归
	dll := windows.NewLazySystemDLL("kernel32.dll")
	proc := dll.NewProc("VirtualQuery")

	ret, _, err := proc.Call(
		addr,                          // lpAddress
		uintptr(unsafe.Pointer(&mbi)), // lpBuffer
		unsafe.Sizeof(mbi),            // dwLength
	)

	if err != nil && err.Error() != "The operation completed successfully." {
		return false
	}

	if ret == 0 {
		return false
	}

	// 检查内存是否已提交且可读
	return (mbi.State == 0x1000) && // MEM_COMMIT
		(mbi.Protect&0x01 == 0) && // 不是PAGE_NOACCESS
		(mbi.Protect&0x100 == 0) // 不是PAGE_GUARD
}
