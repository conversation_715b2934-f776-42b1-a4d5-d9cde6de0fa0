package hash

import (
	"fmt"
	"strings"
	"unsafe"
)

// 通过哈希获取DLL基址 - 使用安全的PEB获取避免递归
func (ah *APIHasher) getDLLBaseByHash(dllHash uint32) (uintptr, error) {
	// 遍历PEB中的模块列表 - 使用安全的PEB获取方法避免递归
	peb := PEBInstance.GetPEBSafe()
	if peb == 0 {
		return 0, fmt.Errorf("failed to get PEB")
	}

	ldr := ah.getLdrData(peb)
	if ldr == 0 {
		return 0, fmt.Errorf("failed to get LDR data")
	}

	// InLoadOrderModuleList是一个双向链表
	moduleListHead := ldr + 0x10
	current := *(*uintptr)(unsafe.Pointer(moduleListHead)) // 第一个模块

	ah.logf("Starting module enumeration from: 0x%x", current)

	count := 0
	for current != 0 && current != moduleListHead && count < 100 { // 防止无限循环
		// LDR_DATA_TABLE_ENTRY结构
		// current指向LIST_ENTRY，这就是LDR_DATA_TABLE_ENTRY的开始
		module := current

		dllName := ah.getModuleName(module)
		ah.logf("Found module: %s", dllName)

		if dllName != "" {
			// 提取文件名部分（去掉路径）
			fileName := dllName
			if lastSlash := strings.LastIndex(dllName, "\\"); lastSlash != -1 {
				fileName = dllName[lastSlash+1:]
			}

			ah.logf("Comparing filename: %s (hash: 0x%08x)", fileName, ah.CalculateHash(fileName))

			if ah.CalculateHash(fileName) == dllHash {
				base := ah.getModuleBase(module)
				ah.logf("Found target DLL at base: 0x%x", base)
				return base, nil
			}
		}

		// 移动到下一个模块
		current = *(*uintptr)(unsafe.Pointer(current))
		count++
	}

	return 0, fmt.Errorf("DLL not found after checking %d modules", count)
}

// 安全的DLL基址获取 - 专门用于避免递归调用
func (ah *APIHasher) GetDLLBaseByHashSafe(dllHash uint32) (uintptr, error) {
	// 使用安全的PEB获取方法
	peb := PEBInstance.GetPEBSafe()
	if peb == 0 {
		return 0, fmt.Errorf("failed to get PEB safely")
	}

	ldr := ah.getLdrData(peb)
	if ldr == 0 {
		return 0, fmt.Errorf("failed to get LDR data")
	}

	// InLoadOrderModuleList是一个双向链表
	moduleListHead := ldr + 0x10
	current := *(*uintptr)(unsafe.Pointer(moduleListHead)) // 第一个模块

	ah.logf("Safe module enumeration from: 0x%x", current)

	count := 0
	for current != 0 && current != moduleListHead && count < 100 { // 防止无限循环
		module := current

		dllName := ah.getModuleName(module)
		ah.logf("Safe found module: %s", dllName)

		if dllName != "" {
			// 提取文件名部分（去掉路径）
			fileName := dllName
			if lastSlash := strings.LastIndex(dllName, "\\"); lastSlash != -1 {
				fileName = dllName[lastSlash+1:]
			}

			ah.logf("Safe comparing filename: %s (hash: 0x%08x)", fileName, ah.CalculateHash(fileName))

			if ah.CalculateHash(fileName) == dllHash {
				base := ah.getModuleBase(module)
				ah.logf("Safe found target DLL at base: 0x%x", base)
				return base, nil
			}
		}

		// 移动到下一个模块
		current = *(*uintptr)(unsafe.Pointer(current))
		count++
	}

	ah.logf("❌ Safe DLL not found: hash=0x%08x", dllHash)
	return 0, fmt.Errorf("DLL not found safely")
}
