package hash

import (
	"etwloader/pe"
	"fmt"
	"strings"
	"unsafe"
)

// 通过哈希获取API地址 - 带保障机制和统计
func (ah *APIHasher) GetAPIByHash(dllHash, apiHash uint32) (uintptr, error) {
	// 首先检查预计算的哈希表
	dllName, dllExists := dllHashes[dllHash]
	apiName, apiExists := apiHashes[apiHash]

	// 检查动态哈希表
	if !dllExists {
		if dynamicDllName, exists := ah.dynamicHashes[dllHash]; exists {
			dllName = dynamicDllName
			dllExists = true
			ah.logf("📚 Found DLL in dynamic hash table: %s", dllName)
		}
	}

	if !apiExists {
		if dynamicApiName, exists := ah.dynamicHashes[apiHash]; exists {
			apiName = dynamicApiName
			apiExists = true
			ah.logf("📚 Found API in dynamic hash table: %s", apiName)
		}
	}

	if !dllExists || !apiExists {
		ah.hashMisses++
		ah.logf("⚠️ Hash not found (misses: %d, hits: %d), trying dynamic resolution", ah.hashMisses, ah.hashHits)
		return ah.getDynamicAPIByHash(dllHash, apiHash)
	}

	ah.hashHits++
	ah.logf("✅ Resolving %s!%s via hash (hits: %d, misses: %d)", dllName, apiName, ah.hashHits, ah.hashMisses)

	// 1. 获取DLL基址
	dllBase, err := ah.getDLLBaseByHash(dllHash)
	if err != nil {
		ah.logf("⚠️ Failed to get DLL base via hash, trying dynamic resolution: %v", err)
		return ah.getDynamicAPIByHash(dllHash, apiHash)
	}

	// 2. 解析导出表
	exportDir := pe.GetExportDirectory(dllBase)
	if exportDir == 0 {
		ah.logf("⚠️ No export directory found, trying dynamic resolution")
		return ah.getDynamicAPIByHash(dllHash, apiHash)
	}

	// 3. 遍历导出函数查找匹配哈希
	addr, err := ah.GetFunctionByHash(dllBase, exportDir, apiHash)
	if err != nil {
		ah.logf("⚠️ Function not found via hash, trying dynamic resolution: %v", err)
		return ah.getDynamicAPIByHash(dllHash, apiHash)
	}

	return addr, nil
}

// 动态哈希解析 - 保障机制
func (ah *APIHasher) getDynamicAPIByHash(dllHash, apiHash uint32) (uintptr, error) {
	ah.logf("🔄 Starting dynamic hash resolution")

	// 遍历所有已加载的模块
	peb := PEBInstance.GetPEB()
	if peb == 0 {
		return 0, fmt.Errorf("failed to get PEB for dynamic resolution")
	}

	ldr := ah.getLdrData(peb)
	if ldr == 0 {
		return 0, fmt.Errorf("failed to get LDR data for dynamic resolution")
	}

	moduleListHead := ldr + 0x10
	current := *(*uintptr)(unsafe.Pointer(moduleListHead))

	count := 0
	for current != 0 && current != moduleListHead && count < 100 {
		module := current

		dllName := ah.getModuleName(module)
		if dllName != "" {
			// 提取文件名
			fileName := dllName
			if lastSlash := strings.LastIndex(dllName, "\\"); lastSlash != -1 {
				fileName = dllName[lastSlash+1:]
			}

			// 检查DLL哈希是否匹配
			if ah.CalculateHash(fileName) == dllHash {
				ah.logf("✅ Found matching DLL dynamically: %s", fileName)

				// 自动学习：将发现的DLL添加到动态哈希表
				if ah.autoUpdateHashes {
					ah.dynamicHashes[dllHash] = fileName
					ah.logf("📚 Learned new DLL hash: 0x%08x -> %s", dllHash, fileName)
				}

				// 获取模块基址并查找API
				base := ah.getModuleBase(module)
				exportDir := pe.GetExportDirectory(base)
				if exportDir != 0 {
					addr, err := ah.getFunctionByHashWithLearning(base, exportDir, apiHash)
					if err == nil {
						ah.logf("✅ Found API dynamically at: 0x%x", addr)
						return addr, nil
					}
				}
			}
		}

		current = *(*uintptr)(unsafe.Pointer(current))
		count++
	}

	return 0, fmt.Errorf("dynamic hash resolution failed after checking %d modules", count)
}
