package hash

import (
	"fmt"
	"unsafe"
)

// 预定义的系统调用函数 - 提供类型安全的接口

// NtAllocateVirtualMemory - 分配虚拟内存
func (ds *directSysCall) NtAllocateVirtualMemory(processHandle uintptr, baseAddress *uintptr, zeroBits uintptr, regionSize *uintptr, allocationType, protect uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtAllocateVirtualMemory syscall")

	// 获取系统调用号
	syscallNum, err := GetSyscallNumber("NtAllocateVirtualMemory")
	if err != nil {
		ds.logf("⚠️ Failed to get syscall number, trying dynamic extraction: %v", err)
		syscallNum, err = ds.getSyscallNumber("NtAllocateVirtualMemory")
		if err != nil {
			ds.logf("⚠️ Dynamic extraction failed, using hardcoded: %v", err)
			syscallNum = 0x18 // 默认值
		}
	}

	// 执行系统调用
	return ds.executeDirectSyscall(syscallNum, 
		processHandle, 
		uintptr(unsafe.Pointer(baseAddress)), 
		zeroBits, 
		uintptr(unsafe.Pointer(regionSize)), 
		allocationType, 
		protect)
}

// NtWriteVirtualMemory - 写入虚拟内存
func (ds *directSysCall) NtWriteVirtualMemory(processHandle, baseAddress, buffer, numberOfBytesToWrite uintptr, numberOfBytesWritten *uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtWriteVirtualMemory syscall")

	syscallNum, err := GetSyscallNumber("NtWriteVirtualMemory")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtWriteVirtualMemory")
		if err != nil {
			syscallNum = 0x3A // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		processHandle, 
		baseAddress, 
		buffer, 
		numberOfBytesToWrite, 
		uintptr(unsafe.Pointer(numberOfBytesWritten)))
}

// NtReadVirtualMemory - 读取虚拟内存
func (ds *directSysCall) NtReadVirtualMemory(processHandle, baseAddress, buffer, numberOfBytesToRead uintptr, numberOfBytesRead *uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtReadVirtualMemory syscall")

	syscallNum, err := GetSyscallNumber("NtReadVirtualMemory")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtReadVirtualMemory")
		if err != nil {
			syscallNum = 0x3F // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		processHandle, 
		baseAddress, 
		buffer, 
		numberOfBytesToRead, 
		uintptr(unsafe.Pointer(numberOfBytesRead)))
}

// NtProtectVirtualMemory - 修改内存保护
func (ds *directSysCall) NtProtectVirtualMemory(processHandle uintptr, baseAddress *uintptr, regionSize *uintptr, newProtect uintptr, oldProtect *uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtProtectVirtualMemory syscall")

	syscallNum, err := GetSyscallNumber("NtProtectVirtualMemory")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtProtectVirtualMemory")
		if err != nil {
			syscallNum = 0x50 // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		processHandle, 
		uintptr(unsafe.Pointer(baseAddress)), 
		uintptr(unsafe.Pointer(regionSize)), 
		newProtect, 
		uintptr(unsafe.Pointer(oldProtect)))
}

// NtFreeVirtualMemory - 释放虚拟内存
func (ds *directSysCall) NtFreeVirtualMemory(processHandle uintptr, baseAddress *uintptr, regionSize *uintptr, freeType uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtFreeVirtualMemory syscall")

	syscallNum, err := GetSyscallNumber("NtFreeVirtualMemory")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtFreeVirtualMemory")
		if err != nil {
			syscallNum = 0x1E // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		processHandle, 
		uintptr(unsafe.Pointer(baseAddress)), 
		uintptr(unsafe.Pointer(regionSize)), 
		freeType)
}

// NtCreateThread - 创建线程
func (ds *directSysCall) NtCreateThread(threadHandle *uintptr, desiredAccess uintptr, objectAttributes, processHandle, clientId, threadContext, initialTeb uintptr, createSuspended bool) (uintptr, error) {
	ds.logf("🔧 Executing NtCreateThread syscall")

	syscallNum, err := GetSyscallNumber("NtCreateThread")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtCreateThread")
		if err != nil {
			syscallNum = 0x4E // 默认值
		}
	}

	suspended := uintptr(0)
	if createSuspended {
		suspended = 1
	}

	return ds.executeDirectSyscall(syscallNum, 
		uintptr(unsafe.Pointer(threadHandle)), 
		desiredAccess, 
		objectAttributes, 
		processHandle, 
		clientId, 
		threadContext, 
		initialTeb, 
		suspended)
}

// NtCreateThreadEx - 创建线程（扩展版本）
func (ds *directSysCall) NtCreateThreadEx(threadHandle *uintptr, desiredAccess uintptr, objectAttributes, processHandle, startRoutine, argument uintptr, createFlags uintptr, stackZeroBits, stackCommit, stackReserve uintptr, attributeList uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtCreateThreadEx syscall")

	syscallNum, err := GetSyscallNumber("NtCreateThreadEx")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtCreateThreadEx")
		if err != nil {
			syscallNum = 0xC1 // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		uintptr(unsafe.Pointer(threadHandle)), 
		desiredAccess, 
		objectAttributes, 
		processHandle, 
		startRoutine, 
		argument, 
		createFlags, 
		stackZeroBits, 
		stackCommit, 
		stackReserve, 
		attributeList)
}

// NtOpenProcess - 打开进程
func (ds *directSysCall) NtOpenProcess(processHandle *uintptr, desiredAccess uintptr, objectAttributes, clientId uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtOpenProcess syscall")

	syscallNum, err := GetSyscallNumber("NtOpenProcess")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtOpenProcess")
		if err != nil {
			syscallNum = 0x26 // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		uintptr(unsafe.Pointer(processHandle)), 
		desiredAccess, 
		objectAttributes, 
		clientId)
}

// NtCreateSection - 创建节对象
func (ds *directSysCall) NtCreateSection(sectionHandle *uintptr, desiredAccess uintptr, objectAttributes, maximumSize uintptr, sectionPageProtection, allocationAttributes, fileHandle uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtCreateSection syscall")

	syscallNum, err := GetSyscallNumber("NtCreateSection")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtCreateSection")
		if err != nil {
			syscallNum = 0x4A // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		uintptr(unsafe.Pointer(sectionHandle)), 
		desiredAccess, 
		objectAttributes, 
		maximumSize, 
		sectionPageProtection, 
		allocationAttributes, 
		fileHandle)
}

// NtMapViewOfSection - 映射节视图
func (ds *directSysCall) NtMapViewOfSection(sectionHandle, processHandle uintptr, baseAddress *uintptr, zeroBits, commitSize uintptr, sectionOffset *uintptr, viewSize *uintptr, inheritDisposition, allocationType, win32Protect uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtMapViewOfSection syscall")

	syscallNum, err := GetSyscallNumber("NtMapViewOfSection")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtMapViewOfSection")
		if err != nil {
			syscallNum = 0x28 // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		sectionHandle, 
		processHandle, 
		uintptr(unsafe.Pointer(baseAddress)), 
		zeroBits, 
		commitSize, 
		uintptr(unsafe.Pointer(sectionOffset)), 
		uintptr(unsafe.Pointer(viewSize)), 
		inheritDisposition, 
		allocationType, 
		win32Protect)
}

// NtUnmapViewOfSection - 取消映射节视图
func (ds *directSysCall) NtUnmapViewOfSection(processHandle, baseAddress uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtUnmapViewOfSection syscall")

	syscallNum, err := GetSyscallNumber("NtUnmapViewOfSection")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtUnmapViewOfSection")
		if err != nil {
			syscallNum = 0x2A // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, processHandle, baseAddress)
}

// NtQueryInformationProcess - 查询进程信息
func (ds *directSysCall) NtQueryInformationProcess(processHandle, processInformationClass uintptr, processInformation uintptr, processInformationLength uintptr, returnLength *uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtQueryInformationProcess syscall")

	syscallNum, err := GetSyscallNumber("NtQueryInformationProcess")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtQueryInformationProcess")
		if err != nil {
			syscallNum = 0x19 // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		processHandle, 
		processInformationClass, 
		processInformation, 
		processInformationLength, 
		uintptr(unsafe.Pointer(returnLength)))
}

// NtResumeThread - 恢复线程
func (ds *directSysCall) NtResumeThread(threadHandle uintptr, previousSuspendCount *uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtResumeThread syscall")

	syscallNum, err := GetSyscallNumber("NtResumeThread")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtResumeThread")
		if err != nil {
			syscallNum = 0x52 // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		threadHandle, 
		uintptr(unsafe.Pointer(previousSuspendCount)))
}

// NtSuspendThread - 挂起线程
func (ds *directSysCall) NtSuspendThread(threadHandle uintptr, previousSuspendCount *uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtSuspendThread syscall")

	syscallNum, err := GetSyscallNumber("NtSuspendThread")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtSuspendThread")
		if err != nil {
			syscallNum = 0x1FB // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, 
		threadHandle, 
		uintptr(unsafe.Pointer(previousSuspendCount)))
}

// NtClose - 关闭句柄
func (ds *directSysCall) NtClose(handle uintptr) (uintptr, error) {
	ds.logf("🔧 Executing NtClose syscall")

	syscallNum, err := GetSyscallNumber("NtClose")
	if err != nil {
		syscallNum, err = ds.getSyscallNumber("NtClose")
		if err != nil {
			syscallNum = 0x0F // 默认值
		}
	}

	return ds.executeDirectSyscall(syscallNum, handle)
}

// 便捷函数：通过名称调用系统调用
func (ds *directSysCall) CallSyscallByName(name string, args ...uintptr) (uintptr, error) {
	ds.logf("🔧 Calling syscall by name: %s", name)

	if !IsSyscallSupported(name) {
		return 0, fmt.Errorf("unsupported syscall: %s", name)
	}

	syscallNum, err := GetSyscallNumber(name)
	if err != nil {
		ds.logf("⚠️ Failed to get syscall number from mapping, trying dynamic: %v", err)
		syscallNum, err = ds.getSyscallNumber(name)
		if err != nil {
			return 0, fmt.Errorf("failed to get syscall number: %v", err)
		}
	}

	return ds.executeDirectSyscall(syscallNum, args...)
}

// 获取支持的系统调用列表
func (ds *directSysCall) GetSupportedSyscalls() []string {
	return GetSupportedSyscalls()
}

// 检查系统调用是否支持
func (ds *directSysCall) IsSyscallSupported(name string) bool {
	return IsSyscallSupported(name)
}
