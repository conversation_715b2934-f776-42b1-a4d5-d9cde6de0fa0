package hash

import (
	"etwloader/pe"
	"fmt"
	"golang.org/x/sys/windows"
	"unsafe"
)

type PEB struct {
	verbose bool
}

func NewPEB(verbose bool) *PEB {
	return &PEB{
		verbose: verbose,
	}
}

var PEBInstance = NewPEB(true)

// 获取PEB地址 - 真正的实现
func (p *PEB) GetPEB() uintptr {
	// 尝试多种方法获取PEB，按优先级排序

	// 方法1: 直接从TEB获取（最快，最隐蔽）
	if peb := p.GetPEBFromTEB(); peb != 0 {
		p.logf("✅ PEB from TEB: 0x%x", peb)
		return peb
	}

	// 方法2: 通过NtQueryInformationProcess获取
	if peb := p.GetPEBAlternative(); peb != 0 {
		p.logf("✅ PEB from NtQueryInformationProcess: 0x%x", peb)
		return peb
	}

	// 方法3: 通过已知的PEB位置搜索
	if peb := p.searchPEBInMemory(); peb != 0 {
		p.logf("✅ PEB from memory search: 0x%x", peb)
		return peb
	}

	p.logf("❌ Failed to Get PEB using all methods")
	return 0
}

// 安全的PEB获取 - 专门用于避免递归调用
func (p *PEB) GetPEBSafe() uintptr {
	// 只使用最基本的方法，避免任何可能导致递归的API调用

	// 方法1: 直接从TEB获取（最安全）
	if peb := p.GetPEBFromTEB(); peb != 0 {
		p.logf("✅ Safe PEB from TEB: 0x%x", peb)
		return peb
	}

	// 方法2: 使用直接调用的NtQueryInformationProcess
	if peb := p.GetPEBWithDirectCall(); peb != 0 {
		p.logf("✅ Safe PEB from direct call: 0x%x", peb)
		return peb
	}

	p.logf("❌ Failed to Get PEB safely")
	return 0
}

// 从TEB直接获取PEB - 最隐蔽的方法
func (p *PEB) GetPEBFromTEB() uintptr {
	// 在Go中，我们可以通过一些技巧来访问TEB
	// 虽然不能直接使用内联汇编，但可以通过其他方式

	if unsafe.Sizeof(uintptr(0)) == 8 {
		// x64系统：尝试通过已知的TEB结构获取
		return p.GetPEBFromTEBx64()
	} else {
		// x86系统：尝试通过已知的TEB结构获取
		return p.GetPEBFromTEBx86()
	}
}

// x64系统从TEB获取PEB
func (p *PEB) GetPEBFromTEBx64() uintptr {
	// 在x64系统中，我们可以通过一些技巧来获取TEB
	// 方法：利用Go运行时的一些内部结构

	// 尝试通过栈地址推算TEB位置
	var stackVar uintptr
	stackAddr := uintptr(unsafe.Pointer(&stackVar))

	// TEB通常在栈的附近，我们可以搜索特征
	return TEBInstance.findTEBNearStack(stackAddr)
}

// x86系统从TEB获取PEB
func (p *PEB) GetPEBFromTEBx86() uintptr {
	// x86系统的实现
	var stackVar uintptr
	stackAddr := uintptr(unsafe.Pointer(&stackVar))

	return TEBInstance.findTEBNearStack(stackAddr)
}

// 验证是否是有效的PEB - 直接调用版本
func (p *PEB) ValidatePEBWithDirectCall(peb uintptr) bool {
	// PEB的一些特征验证 - 使用直接调用避免递归
	// 1. 检查Ldr指针
	ldr := *(*uintptr)(unsafe.Pointer(peb + 0x18))
	if ldr == 0 || !CheckerInstance.IsAddressReadableWithDirectCall(ldr) {
		return false
	}

	// 2. 检查进程参数
	processParams := *(*uintptr)(unsafe.Pointer(peb + 0x20))
	if processParams == 0 || !CheckerInstance.IsAddressReadableWithDirectCall(processParams) {
		return false
	}

	// 3. 检查镜像基址
	imageBase := *(*uintptr)(unsafe.Pointer(peb + 0x10))
	if imageBase == 0 || !CheckerInstance.IsAddressReadableWithDirectCall(imageBase) {
		return false
	}

	// 验证镜像基址是否是有效的PE文件
	dosHeader := (*pe.IMAGE_DOS_HEADER)(unsafe.Pointer(imageBase))
	if dosHeader.E_magic != 0x5A4D { // "MZ"
		return false
	}

	return true
}

// 在内存中搜索PEB - 使用直接调用避免递归
func (p *PEB) searchPEBInMemory() uintptr {
	// 作为最后的手段，在进程内存中搜索PEB特征
	p.logf("🔍 Searching for PEB in process memory (direct call)")

	// 获取进程内存信息 - 使用直接调用避免递归
	type SystemInfo struct {
		ProcessorArchitecture     uint16
		Reserved                  uint16
		PageSize                  uint32
		MinimumApplicationAddress uintptr
		MaximumApplicationAddress uintptr
		ActiveProcessorMask       uintptr
		NumberOfProcessors        uint32
		ProcessorType             uint32
		AllocationGranularity     uint32
		ProcessorLevel            uint16
		ProcessorRevision         uint16
	}

	var si SystemInfo

	// 使用直接调用GetSystemInfo避免递归
	dll := windows.NewLazySystemDLL("kernel32.dll")
	proc := dll.NewProc("GetSystemInfo")

	ret, _, err := proc.Call(uintptr(unsafe.Pointer(&si)))

	if err != nil && err.Error() != "The operation completed successfully." {
		p.logf("❌ Direct GetSystemInfo failed: %v", err)
		return p.searchPEBWithHardcodedRanges()
	}

	if ret != 0 {
		p.logf("❌ GetSystemInfo returned non-zero: %d", ret)
		return p.searchPEBWithHardcodedRanges()
	}

	// 在用户空间搜索
	minAddr := si.MinimumApplicationAddress
	maxAddr := si.MaximumApplicationAddress
	pageSize := uintptr(si.PageSize)

	if pageSize == 0 {
		pageSize = 0x1000 // 默认4KB页面大小
	}

	p.logf("🔍 Searching PEB from 0x%x to 0x%x (page size: 0x%x)", minAddr, maxAddr, pageSize)

	// 限制搜索范围避免过长时间
	searchLimit := uintptr(0x10000000) // 256MB搜索限制
	if maxAddr-minAddr > searchLimit {
		maxAddr = minAddr + searchLimit
		p.logf("🔍 Limited search range to 0x%x", maxAddr)
	}

	for addr := minAddr; addr < maxAddr; addr += pageSize {
		if CheckerInstance.IsAddressReadableWithDirectCall(addr) && p.ValidatePEBWithDirectCall(addr) {
			p.logf("✅ Found PEB at: 0x%x", addr)
			return addr
		}
	}

	p.logf("❌ PEB not found in memory search")
	return 0
}

// 使用硬编码范围搜索PEB（最后的备用方案）
func (p *PEB) searchPEBWithHardcodedRanges() uintptr {
	p.logf("🔍 Using hardcoded ranges for PEB search")

	// Windows用户空间的典型范围
	minAddr := uintptr(0x10000)    // 64KB
	maxAddr := uintptr(0x10000000) // 256MB（限制搜索范围）
	pageSize := uintptr(0x1000)    // 4KB页面大小

	p.logf("🔍 Searching PEB with hardcoded ranges from 0x%x to 0x%x", minAddr, maxAddr)

	for addr := minAddr; addr < maxAddr; addr += pageSize {
		if CheckerInstance.IsAddressReadableWithDirectCall(addr) && p.ValidatePEBWithDirectCall(addr) {
			p.logf("✅ Found PEB with hardcoded ranges at: 0x%x", addr)
			return addr
		}
	}

	p.logf("❌ PEB not found with hardcoded ranges")
	return 0
}

// 替代方案：通过已知的方法获取PEB，避免直接API调用
func (p *PEB) GetPEBAlternative() uintptr {
	// 对于初始的PEB获取，这是一个鸡生蛋的问题
	// 我们使用直接调用作为最后的保障，但首先尝试哈希调用

	// ProcessBasicInformation = 0
	type ProcessBasicInformation struct {
		ExitStatus                   uintptr
		PebBaseAddress               uintptr
		AffinityMask                 uintptr
		BasePriority                 uintptr
		UniqueProcessId              uintptr
		InheritedFromUniqueProcessId uintptr
	}

	var pbi ProcessBasicInformation
	var returnLength uint32

	// 首先尝试哈希调用
	ret, err := APIHasherInstance.CallAPI("ntdll.dll", "NtQueryInformationProcess",
		uintptr(windows.CurrentProcess()),      // ProcessHandle
		0,                                      // ProcessInformationClass (ProcessBasicInformation)
		uintptr(unsafe.Pointer(&pbi)),          // ProcessInformation
		unsafe.Sizeof(pbi),                     // ProcessInformationLength
		uintptr(unsafe.Pointer(&returnLength)), // ReturnLength
	)

	if err != nil || ret != 0 {
		p.logf("⚠️ Hash-based NtQueryInformationProcess failed, using direct call")
		// 回退到直接调用
		return p.GetPEBWithDirectCall()
	}

	p.logf("✅ PEB from hash-based NtQueryInformationProcess: 0x%x", pbi.PebBaseAddress)
	return pbi.PebBaseAddress
}

// 直接调用获取PEB（保障机制）
func (p *PEB) GetPEBWithDirectCall() uintptr {
	dll := windows.NewLazySystemDLL("ntdll.dll")
	proc := dll.NewProc("NtQueryInformationProcess")

	type ProcessBasicInformation struct {
		ExitStatus                   uintptr
		PebBaseAddress               uintptr
		AffinityMask                 uintptr
		BasePriority                 uintptr
		UniqueProcessId              uintptr
		InheritedFromUniqueProcessId uintptr
	}

	var pbi ProcessBasicInformation
	var returnLength uint32

	ret, _, _ := proc.Call(
		uintptr(windows.CurrentProcess()),
		0,
		uintptr(unsafe.Pointer(&pbi)),
		unsafe.Sizeof(pbi),
		uintptr(unsafe.Pointer(&returnLength)),
	)

	if ret != 0 {
		p.logf("❌ Direct NtQueryInformationProcess failed: 0x%x", ret)
		return 0
	}

	p.logf("✅ PEB from direct NtQueryInformationProcess: 0x%x", pbi.PebBaseAddress)
	return pbi.PebBaseAddress
}

func (p *PEB) logf(format string, args ...interface{}) {
	if p.verbose {
		fmt.Printf("[API_HASH] "+format+"\n", args...)
	}
}
