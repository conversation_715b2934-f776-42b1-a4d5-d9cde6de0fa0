package pe

import "unsafe"

type IMAGE_DATA_DIRECTORY struct {
	VirtualAddress uint32
	Size           uint32
}

type IMAGE_EXPORT_DIRECTORY struct {
	Characteristics       uint32
	TimeDateStamp         uint32
	MajorVersion          uint16
	MinorVersion          uint16
	Name                  uint32
	Base                  uint32
	NumberOfFunctions     uint32
	NumberOfNames         uint32
	AddressOfFunctions    uint32
	AddressOfNames        uint32
	AddressOfNameOrdinals uint32
}

// 获取导出目录
func GetExportDirectory(dllBase uintptr) uintptr {
	// 解析PE头
	dosHeader := (*IMAGE_DOS_HEADER)(unsafe.Pointer(dllBase))
	if dosHeader.E_magic != 0x5A4D { // "MZ"
		return 0
	}

	ntHeaders := (*IMAGE_NT_HEADERS)(unsafe.Pointer(dllBase + uintptr(dosHeader.E_lfanew)))
	if ntHeaders.Signature != 0x00004550 { // "PE\0\0"
		return 0
	}

	exportRVA := ntHeaders.OptionalHeader.DataDirectory[0].VirtualAddress
	if exportRVA == 0 {
		return 0
	}

	return dllBase + uintptr(exportRVA)
}
