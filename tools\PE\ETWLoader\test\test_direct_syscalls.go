package main

import (
	"fmt"
	"os"
	"unsafe"
	
	"etwloader/hash"
)

func main() {
	fmt.Println("🧪 Testing Direct System Calls Implementation")
	fmt.Println("=============================================")

	// 测试系统调用映射表
	testSyscallMapping()
	
	// 测试预定义系统调用
	testPredefinedSyscalls()
	
	// 测试动态系统调用号获取
	testDynamicSyscallExtraction()
	
	// 测试实际的系统调用执行
	testActualSyscallExecution()
	
	fmt.Println("\n🎯 All tests completed!")
}

func testSyscallMapping() {
	fmt.Println("\n--- Testing Syscall Mapping ---")
	
	// 测试获取支持的系统调用
	supported := hash.GetSupportedSyscalls()
	fmt.Printf("✅ Supported syscalls: %d\n", len(supported))
	
	for i, name := range supported {
		if i < 5 { // 只显示前5个
			num, err := hash.GetSyscallNumber(name)
			if err != nil {
				fmt.Printf("❌ Failed to get syscall number for %s: %v\n", name, err)
			} else {
				fmt.Printf("   %s -> 0x%x\n", name, num)
			}
		}
	}
	
	// 测试搜索功能
	matches := hash.SearchSyscalls("memory")
	fmt.Printf("✅ Memory-related syscalls: %v\n", matches)
	
	// 测试支持检查
	if hash.IsSyscallSupported("NtAllocateVirtualMemory") {
		fmt.Println("✅ NtAllocateVirtualMemory is supported")
	}
	
	if !hash.IsSyscallSupported("NonExistentSyscall") {
		fmt.Println("✅ NonExistentSyscall correctly reported as unsupported")
	}
}

func testPredefinedSyscalls() {
	fmt.Println("\n--- Testing Predefined Syscalls ---")
	
	// 获取支持的系统调用列表
	supported := hash.DirectSysCall.GetSupportedSyscalls()
	fmt.Printf("✅ DirectSysCall supports %d syscalls\n", len(supported))
	
	// 测试是否支持检查
	if hash.DirectSysCall.IsSyscallSupported("NtAllocateVirtualMemory") {
		fmt.Println("✅ NtAllocateVirtualMemory is supported by DirectSysCall")
	}
	
	// 测试通过名称调用（不实际执行，只测试解析）
	fmt.Println("✅ Predefined syscall functions are available")
}

func testDynamicSyscallExtraction() {
	fmt.Println("\n--- Testing Dynamic Syscall Extraction ---")

	// 测试动态获取系统调用号
	testFunctions := []string{
		"NtAllocateVirtualMemory",
		"NtWriteVirtualMemory", 
		"NtReadVirtualMemory",
		"NtProtectVirtualMemory",
		"NtCreateThread",
	}
	
	for _, funcName := range testFunctions {
		// 这里我们不能直接调用私有方法，所以通过公共接口测试
		if num, err := hash.GetSyscallNumber(funcName); err == nil {
			fmt.Printf("✅ %s -> 0x%x (from mapping)\n", funcName, num)
		} else {
			fmt.Printf("⚠️ %s not found in mapping: %v\n", funcName, err)
		}
	}
}

func testActualSyscallExecution() {
	fmt.Println("\n--- Testing Actual Syscall Execution ---")
	
	// 测试1: NtAllocateVirtualMemory
	fmt.Println("🔧 Testing NtAllocateVirtualMemory...")
	
	var baseAddr uintptr = 0
	var regionSize uintptr = 4096 // 4KB
	
	result, err := hash.DirectSysCall.NtAllocateVirtualMemory(
		uintptr(0xFFFFFFFFFFFFFFFF), // Current process (-1)
		&baseAddr,
		0,           // ZeroBits
		&regionSize,
		0x3000,      // MEM_COMMIT | MEM_RESERVE
		0x04,        // PAGE_READWRITE
	)
	
	if err != nil {
		fmt.Printf("⚠️ NtAllocateVirtualMemory failed: %v (result: 0x%x)\n", err, result)
	} else {
		fmt.Printf("✅ NtAllocateVirtualMemory succeeded: allocated at 0x%x, size: %d\n", baseAddr, regionSize)
		
		// 测试2: NtWriteVirtualMemory
		fmt.Println("🔧 Testing NtWriteVirtualMemory...")
		
		testData := []byte("Hello, Direct Syscalls!")
		var bytesWritten uintptr
		
		writeResult, writeErr := hash.DirectSysCall.NtWriteVirtualMemory(
			uintptr(0xFFFFFFFFFFFFFFFF), // Current process
			baseAddr,
			uintptr(unsafe.Pointer(&testData[0])),
			uintptr(len(testData)),
			&bytesWritten,
		)
		
		if writeErr != nil {
			fmt.Printf("⚠️ NtWriteVirtualMemory failed: %v (result: 0x%x)\n", writeErr, writeResult)
		} else {
			fmt.Printf("✅ NtWriteVirtualMemory succeeded: wrote %d bytes\n", bytesWritten)
		}
		
		// 测试3: NtReadVirtualMemory
		fmt.Println("🔧 Testing NtReadVirtualMemory...")
		
		readBuffer := make([]byte, len(testData))
		var bytesRead uintptr
		
		readResult, readErr := hash.DirectSysCall.NtReadVirtualMemory(
			uintptr(0xFFFFFFFFFFFFFFFF), // Current process
			baseAddr,
			uintptr(unsafe.Pointer(&readBuffer[0])),
			uintptr(len(readBuffer)),
			&bytesRead,
		)
		
		if readErr != nil {
			fmt.Printf("⚠️ NtReadVirtualMemory failed: %v (result: 0x%x)\n", readErr, readResult)
		} else {
			fmt.Printf("✅ NtReadVirtualMemory succeeded: read %d bytes: %s\n", bytesRead, string(readBuffer[:bytesRead]))
		}
		
		// 测试4: NtFreeVirtualMemory
		fmt.Println("🔧 Testing NtFreeVirtualMemory...")
		
		freeResult, freeErr := hash.DirectSysCall.NtFreeVirtualMemory(
			uintptr(0xFFFFFFFFFFFFFFFF), // Current process
			&baseAddr,
			&regionSize,
			0x8000, // MEM_RELEASE
		)
		
		if freeErr != nil {
			fmt.Printf("⚠️ NtFreeVirtualMemory failed: %v (result: 0x%x)\n", freeErr, freeResult)
		} else {
			fmt.Printf("✅ NtFreeVirtualMemory succeeded\n")
		}
	}
	
	// 测试5: 通过名称调用系统调用
	fmt.Println("🔧 Testing CallSyscallByName...")
	
	// 测试NtClose（使用无效句柄，应该返回错误但不会崩溃）
	closeResult, closeErr := hash.DirectSysCall.CallSyscallByName("NtClose", 0x12345678)
	fmt.Printf("✅ CallSyscallByName(NtClose) result: 0x%x, err: %v\n", closeResult, closeErr)
}

func init() {
	// 确保在Windows上运行
	if os.Getenv("GOOS") != "" && os.Getenv("GOOS") != "windows" {
		fmt.Println("❌ This test must run on Windows")
		os.Exit(1)
	}
}
