package hash

import (
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// HashManager - 哈希管理器，提供统计、导出、重置等功能
type HashManager struct {
	apiHasher *APIHasher
	verbose   bool
}

// NewHashManager - 创建新的哈希管理器
func NewHashManager(apiHasher *APIHasher, verbose bool) *HashManager {
	return &HashManager{
		apiHasher: apiHasher,
		verbose:   verbose,
	}
}

// ShowDetailedStats - 显示详细的哈希统计信息
func (hm *HashManager) ShowDetailedStats() {
	fmt.Println("\n📊 DETAILED HASH STATISTICS")
	fmt.Println("═══════════════════════════════════════")
	
	// 获取基本统计
	hits, misses, dynamicCount := hm.apiHasher.GetHashStats()
	totalAttempts := hits + misses
	hitRate := float64(0)
	if totalAttempts > 0 {
		hitRate = float64(hits) / float64(totalAttempts) * 100
	}

	fmt.Printf("🎯 Hash Performance:\n")
	fmt.Printf("   Total Attempts: %d\n", totalAttempts)
	fmt.Printf("   Successful Hits: %d\n", hits)
	fmt.Printf("   Cache Misses: %d\n", misses)
	fmt.Printf("   Hit Rate: %.2f%%\n", hitRate)
	fmt.Printf("   Dynamic Hashes Learned: %d\n", dynamicCount)
	
	// 性能评级
	var rating string
	switch {
	case hitRate >= 95:
		rating = "🏆 EXCELLENT"
	case hitRate >= 85:
		rating = "✅ GOOD"
	case hitRate >= 70:
		rating = "⚠️ FAIR"
	default:
		rating = "❌ POOR"
	}
	fmt.Printf("   Performance Rating: %s\n", rating)
	
	// 显示学习到的哈希
	if dynamicCount > 0 && hm.verbose {
		fmt.Printf("\n📚 Dynamically Learned Hashes:\n")
		learned := hm.apiHasher.ExportLearnedHashes()
		for hash, name := range learned {
			fmt.Printf("   0x%08x -> %s\n", hash, name)
		}
	}
	
	fmt.Println("═══════════════════════════════════════")
}

// ExportHashesToFile - 导出学习到的哈希到文件
func (hm *HashManager) ExportHashesToFile(filename string) error {
	learned := hm.apiHasher.ExportLearnedHashes()
	if len(learned) == 0 {
		return fmt.Errorf("no dynamic hashes to export")
	}

	// 创建导出目录
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %v", err)
	}

	// 创建文件
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create file: %v", err)
	}
	defer file.Close()

	// 写入头部信息
	fmt.Fprintf(file, "// Dynamically learned API hashes\n")
	fmt.Fprintf(file, "// Generated on: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Fprintf(file, "// Total hashes: %d\n\n", len(learned))

	// 写入哈希映射
	fmt.Fprintf(file, "var learnedHashes = map[uint32]string{\n")
	for hash, name := range learned {
		fmt.Fprintf(file, "\t0x%08x: \"%s\",\n", hash, name)
	}
	fmt.Fprintf(file, "}\n")

	fmt.Printf("✅ Exported %d learned hashes to: %s\n", len(learned), filename)
	return nil
}

// ResetAllStats - 重置所有统计信息
func (hm *HashManager) ResetAllStats() {
	hm.apiHasher.ResetStats()
	fmt.Println("🔄 All hash statistics have been reset")
}

// EnableAutoUpdate - 启用自动更新哈希表
func (hm *HashManager) EnableAutoUpdate() {
	hm.apiHasher.SetAutoUpdateHashes(true)
	fmt.Println("🔧 Auto-update hashes enabled")
}

// DisableAutoUpdate - 禁用自动更新哈希表
func (hm *HashManager) DisableAutoUpdate() {
	hm.apiHasher.SetAutoUpdateHashes(false)
	fmt.Println("🔧 Auto-update hashes disabled")
}

// TestHashPerformance - 测试哈希性能
func (hm *HashManager) TestHashPerformance() {
	fmt.Println("\n🧪 TESTING HASH PERFORMANCE")
	fmt.Println("═══════════════════════════════════════")
	
	// 测试常用API的哈希性能
	testAPIs := []struct {
		dll string
		api string
	}{
		{"kernel32.dll", "GetProcAddress"},
		{"kernel32.dll", "LoadLibraryA"},
		{"kernel32.dll", "VirtualAlloc"},
		{"kernel32.dll", "VirtualProtect"},
		{"kernel32.dll", "CreateThread"},
		{"ntdll.dll", "NtAllocateVirtualMemory"},
		{"ntdll.dll", "NtWriteVirtualMemory"},
		{"ntdll.dll", "NtCreateThread"},
	}
	
	startTime := time.Now()
	successCount := 0
	
	for _, test := range testAPIs {
		_, err := hm.apiHasher.CallAPI(test.dll, test.api)
		if err == nil {
			successCount++
			fmt.Printf("✅ %s!%s\n", test.dll, test.api)
		} else {
			fmt.Printf("❌ %s!%s: %v\n", test.dll, test.api, err)
		}
	}
	
	duration := time.Since(startTime)
	fmt.Printf("\n📊 Performance Results:\n")
	fmt.Printf("   APIs Tested: %d\n", len(testAPIs))
	fmt.Printf("   Successful: %d\n", successCount)
	fmt.Printf("   Success Rate: %.1f%%\n", float64(successCount)/float64(len(testAPIs))*100)
	fmt.Printf("   Total Time: %v\n", duration)
	fmt.Printf("   Average Time per API: %v\n", duration/time.Duration(len(testAPIs)))
	
	fmt.Println("═══════════════════════════════════════")
}

// OptimizeHashTable - 优化哈希表性能
func (hm *HashManager) OptimizeHashTable() {
	fmt.Println("🔧 Optimizing hash table...")
	
	// 获取当前统计
	hits, misses, dynamicCount := hm.apiHasher.GetHashStats()
	
	if dynamicCount > 0 {
		fmt.Printf("📚 Found %d dynamic hashes that can be added to precomputed table\n", dynamicCount)
		
		// 导出学习到的哈希用于更新预计算表
		filename := fmt.Sprintf("learned_hashes_%s.go", time.Now().Format("20060102_150405"))
		if err := hm.ExportHashesToFile(filename); err != nil {
			fmt.Printf("⚠️ Failed to export hashes: %v\n", err)
		} else {
			fmt.Printf("💾 Exported learned hashes for integration\n")
		}
	}
	
	if misses > hits/10 { // 如果miss率超过10%
		fmt.Printf("⚠️ High miss rate detected (%.1f%%), consider updating precomputed hashes\n", 
			float64(misses)/float64(hits+misses)*100)
	} else {
		fmt.Printf("✅ Hash table performance is optimal\n")
	}
}

// GetModuleStatistics - 获取模块统计信息（使用之前未使用的函数）
func (hm *HashManager) GetModuleStatistics() {
	fmt.Println("\n📊 MODULE STATISTICS")
	fmt.Println("═══════════════════════════════════════")
	
	// 使用之前未使用的模块遍历函数
	peb := PEBInstance.GetPEB()
	if peb == 0 {
		fmt.Println("❌ Failed to get PEB")
		return
	}
	
	ldr := hm.apiHasher.getLdrData(peb)
	if ldr == 0 {
		fmt.Println("❌ Failed to get LDR")
		return
	}
	
	moduleList := hm.apiHasher.getModuleList(ldr)
	fmt.Printf("📁 Module List Address: 0x%x\n", moduleList)
	
	// 遍历模块（限制数量避免无限循环）
	current := moduleList
	moduleCount := 0
	maxModules := 50 // 安全限制
	
	for i := 0; i < maxModules && current != 0; i++ {
		entry := hm.apiHasher.getModuleEntry(current)
		if entry == 0 {
			break
		}
		
		moduleCount++
		if hm.verbose && moduleCount <= 10 { // 只显示前10个
			fmt.Printf("   Module %d: 0x%x\n", moduleCount, entry)
		}
		
		// 获取下一个模块
		next := hm.apiHasher.getNextModule(current)
		if next == current || next == moduleList { // 避免循环
			break
		}
		current = next
	}
	
	fmt.Printf("📊 Total Modules Found: %d\n", moduleCount)
	fmt.Println("═══════════════════════════════════════")
}

// 全局哈希管理器实例
var GlobalHashManager *HashManager

// 初始化全局哈希管理器
func InitHashManager(verbose bool) {
	GlobalHashManager = NewHashManager(APIHasherInstance, verbose)
	// 如果不是verbose模式，设置为静默
	if !verbose {
		APIHasherInstance.SetSilent(true)
	}
}
