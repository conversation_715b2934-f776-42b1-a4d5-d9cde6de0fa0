package hash

import (
	"fmt"
	"runtime"
	"strings"
	"unsafe"
)

// Windows版本信息
type WindowsVersion struct {
	Major int
	Minor int
	Build int
}

// 系统调用映射表 - 支持多个Windows版本
type SyscallMapping struct {
	Name    string
	Numbers map[string]uint32 // 版本 -> 系统调用号
}

// 预定义的系统调用映射表
// 数据来源：https://j00ru.vexillium.org/syscalls/nt/64/ (权威的Windows系统调用表)
var syscallMappings = map[string]*SyscallMapping{
	"NtAllocateVirtualMemory": {
		Name: "NtAllocateVirtualMemory",
		Numbers: map[string]uint32{
			"win10_1903": 0x18, // Windows 10 1903 (Build 18362)
			"win10_1909": 0x18, // Windows 10 1909 (Build 18363)
			"win10_2004": 0x18, // Windows 10 2004 (Build 19041)
			"win10_20H2": 0x18, // Windows 10 20H2 (Build 19042)
			"win10_21H1": 0x18, // Windows 10 21H1 (Build 19043)
			"win10_21H2": 0x18, // Windows 10 21H2 (Build 19044)
			"win11_21H2": 0x18, // Windows 11 21H2 (Build 22000)
			"win11_22H2": 0x18, // Windows 11 22H2 (Build 22621)
			"default":    0x18, // 默认值
		},
	},
	"NtWriteVirtualMemory": {
		Name: "NtWriteVirtualMemory",
		Numbers: map[string]uint32{
			"win10_1903": 0x3A,
			"win10_1909": 0x3A,
			"win10_2004": 0x3A,
			"win10_20H2": 0x3A,
			"win10_21H1": 0x3A,
			"win10_21H2": 0x3A,
			"win11_21H2": 0x3A,
			"win11_22H2": 0x3A,
			"default":    0x3A,
		},
	},
	"NtReadVirtualMemory": {
		Name: "NtReadVirtualMemory",
		Numbers: map[string]uint32{
			"win10_1903": 0x3F,
			"win10_1909": 0x3F,
			"win10_2004": 0x3F,
			"win10_20H2": 0x3F,
			"win10_21H1": 0x3F,
			"win10_21H2": 0x3F,
			"win11_21H2": 0x3F,
			"win11_22H2": 0x3F,
			"default":    0x3F,
		},
	},
	"NtProtectVirtualMemory": {
		Name: "NtProtectVirtualMemory",
		Numbers: map[string]uint32{
			"win10_1903": 0x50,
			"win10_1909": 0x50,
			"win10_2004": 0x50,
			"win10_20H2": 0x50,
			"win10_21H1": 0x50,
			"win10_21H2": 0x50,
			"win11_21H2": 0x50,
			"win11_22H2": 0x50,
			"default":    0x50,
		},
	},
	"NtFreeVirtualMemory": {
		Name: "NtFreeVirtualMemory",
		Numbers: map[string]uint32{
			"win10_1903": 0x1E,
			"win10_1909": 0x1E,
			"win10_2004": 0x1E,
			"win10_20H2": 0x1E,
			"win10_21H1": 0x1E,
			"win10_21H2": 0x1E,
			"win11_21H2": 0x1E,
			"win11_22H2": 0x1E,
			"default":    0x1E,
		},
	},
	"NtCreateThread": {
		Name: "NtCreateThread",
		Numbers: map[string]uint32{
			"win10_1903": 0x4E,
			"win10_1909": 0x4E,
			"win10_2004": 0x4E,
			"win10_20H2": 0x4E,
			"win10_21H1": 0x4E,
			"win10_21H2": 0x4E,
			"win11_21H2": 0x4E,
			"win11_22H2": 0x4E,
			"default":    0x4E,
		},
	},
	"NtCreateThreadEx": {
		Name: "NtCreateThreadEx",
		Numbers: map[string]uint32{
			"win10_1903": 0xC1,
			"win10_1909": 0xC1,
			"win10_2004": 0xC1,
			"win10_20H2": 0xC1,
			"win10_21H1": 0xC1,
			"win10_21H2": 0xC1,
			"win11_21H2": 0xC1,
			"win11_22H2": 0xC1,
			"default":    0xC1,
		},
	},
	"NtOpenProcess": {
		Name: "NtOpenProcess",
		Numbers: map[string]uint32{
			"win10_1903": 0x26,
			"win10_1909": 0x26,
			"win10_2004": 0x26,
			"win10_20H2": 0x26,
			"win10_21H1": 0x26,
			"win10_21H2": 0x26,
			"win11_21H2": 0x26,
			"win11_22H2": 0x26,
			"default":    0x26,
		},
	},
	"NtCreateProcess": {
		Name: "NtCreateProcess",
		Numbers: map[string]uint32{
			"win10_1903": 0x4D,
			"win10_1909": 0x4D,
			"win10_2004": 0x4D,
			"win10_20H2": 0x4D,
			"win10_21H1": 0x4D,
			"win10_21H2": 0x4D,
			"win11_21H2": 0x4D,
			"win11_22H2": 0x4D,
			"default":    0x4D,
		},
	},
	"NtCreateProcessEx": {
		Name: "NtCreateProcessEx",
		Numbers: map[string]uint32{
			"win10_1903": 0x4F,
			"win10_1909": 0x4F,
			"win10_2004": 0x4F,
			"win10_20H2": 0x4F,
			"win10_21H1": 0x4F,
			"win10_21H2": 0x4F,
			"win11_21H2": 0x4F,
			"win11_22H2": 0x4F,
			"default":    0x4F,
		},
	},
	"NtCreateSection": {
		Name: "NtCreateSection",
		Numbers: map[string]uint32{
			"win10_1903": 0x4A,
			"win10_1909": 0x4A,
			"win10_2004": 0x4A,
			"win10_20H2": 0x4A,
			"win10_21H1": 0x4A,
			"win10_21H2": 0x4A,
			"win11_21H2": 0x4A,
			"win11_22H2": 0x4A,
			"default":    0x4A,
		},
	},
	"NtMapViewOfSection": {
		Name: "NtMapViewOfSection",
		Numbers: map[string]uint32{
			"win10_1903": 0x28,
			"win10_1909": 0x28,
			"win10_2004": 0x28,
			"win10_20H2": 0x28,
			"win10_21H1": 0x28,
			"win10_21H2": 0x28,
			"win11_21H2": 0x28,
			"win11_22H2": 0x28,
			"default":    0x28,
		},
	},
	"NtUnmapViewOfSection": {
		Name: "NtUnmapViewOfSection",
		Numbers: map[string]uint32{
			"win10_1903": 0x2A,
			"win10_1909": 0x2A,
			"win10_2004": 0x2A,
			"win10_20H2": 0x2A,
			"win10_21H1": 0x2A,
			"win10_21H2": 0x2A,
			"win11_21H2": 0x2A,
			"win11_22H2": 0x2A,
			"default":    0x2A,
		},
	},
	"NtQueryInformationProcess": {
		Name: "NtQueryInformationProcess",
		Numbers: map[string]uint32{
			"win10_1903": 0x19,
			"win10_1909": 0x19,
			"win10_2004": 0x19,
			"win10_20H2": 0x19,
			"win10_21H1": 0x19,
			"win10_21H2": 0x19,
			"win11_21H2": 0x19,
			"win11_22H2": 0x19,
			"default":    0x19,
		},
	},
	"NtSetInformationProcess": {
		Name: "NtSetInformationProcess",
		Numbers: map[string]uint32{
			"win10_1903": 0x1C,
			"win10_1909": 0x1C,
			"win10_2004": 0x1C,
			"win10_20H2": 0x1C,
			"win10_21H1": 0x1C,
			"win10_21H2": 0x1C,
			"win11_21H2": 0x1C,
			"win11_22H2": 0x1C,
			"default":    0x1C,
		},
	},
	"NtResumeThread": {
		Name: "NtResumeThread",
		Numbers: map[string]uint32{
			"win10_1903": 0x52,
			"win10_1909": 0x52,
			"win10_2004": 0x52,
			"win10_20H2": 0x52,
			"win10_21H1": 0x52,
			"win10_21H2": 0x52,
			"win11_21H2": 0x52,
			"win11_22H2": 0x52,
			"default":    0x52,
		},
	},
	"NtSuspendThread": {
		Name: "NtSuspendThread",
		Numbers: map[string]uint32{
			"win10_1903": 0x1FB,
			"win10_1909": 0x1FB,
			"win10_2004": 0x1FB,
			"win10_20H2": 0x1FB,
			"win10_21H1": 0x1FB,
			"win10_21H2": 0x1FB,
			"win11_21H2": 0x1FB,
			"win11_22H2": 0x1FB,
			"default":    0x1FB,
		},
	},
	"NtClose": {
		Name: "NtClose",
		Numbers: map[string]uint32{
			"win10_1903": 0x0F,
			"win10_1909": 0x0F,
			"win10_2004": 0x0F,
			"win10_20H2": 0x0F,
			"win10_21H1": 0x0F,
			"win10_21H2": 0x0F,
			"win11_21H2": 0x0F,
			"win11_22H2": 0x0F,
			"default":    0x0F,
		},
	},
}

// 获取当前Windows版本
func getCurrentWindowsVersion() string {
	if runtime.GOOS != "windows" {
		return "default"
	}

	// 使用RtlGetVersion获取真实的Windows版本
	version, err := getRealWindowsVersion()
	if err != nil {
		fmt.Printf("[SYSCALL_MAP] ⚠️ Failed to get Windows version: %v, using default\n", err)
		return "default"
	}

	// 根据版本号映射到我们的版本字符串
	versionStr := mapVersionToString(version)
	fmt.Printf("[SYSCALL_MAP] ✅ Detected Windows version: %s (Build: %d)\n", versionStr, version.Build)
	return versionStr
}

// Windows版本结构
type OSVersionInfo struct {
	Major int
	Minor int
	Build int
}

// 获取真实的Windows版本（绕过兼容性垫片）
func getRealWindowsVersion() (*OSVersionInfo, error) {
	// 使用RtlGetVersion API获取真实版本
	// 这个API不受应用程序兼容性设置影响

	type RTL_OSVERSIONINFOW struct {
		dwOSVersionInfoSize uint32
		dwMajorVersion      uint32
		dwMinorVersion      uint32
		dwBuildNumber       uint32
		dwPlatformId        uint32
		szCSDVersion        [128]uint16
	}

	var osvi RTL_OSVERSIONINFOW
	osvi.dwOSVersionInfoSize = uint32(unsafe.Sizeof(osvi))

	// 尝试调用RtlGetVersion
	ret, err := APIHasherInstance.CallAPI("ntdll.dll", "RtlGetVersion", uintptr(unsafe.Pointer(&osvi)))
	if err != nil || ret != 0 {
		// 回退到GetVersionEx
		return getVersionExFallback()
	}

	return &OSVersionInfo{
		Major: int(osvi.dwMajorVersion),
		Minor: int(osvi.dwMinorVersion),
		Build: int(osvi.dwBuildNumber),
	}, nil
}

// 回退方法：使用GetVersionEx
func getVersionExFallback() (*OSVersionInfo, error) {
	type OSVERSIONINFO struct {
		dwOSVersionInfoSize uint32
		dwMajorVersion      uint32
		dwMinorVersion      uint32
		dwBuildNumber       uint32
		dwPlatformId        uint32
		szCSDVersion        [128]byte
	}

	var osvi OSVERSIONINFO
	osvi.dwOSVersionInfoSize = uint32(unsafe.Sizeof(osvi))

	ret, err := APIHasherInstance.CallAPI("kernel32.dll", "GetVersionExA", uintptr(unsafe.Pointer(&osvi)))
	if err != nil || ret == 0 {
		return nil, fmt.Errorf("GetVersionExA failed: %v", err)
	}

	return &OSVersionInfo{
		Major: int(osvi.dwMajorVersion),
		Minor: int(osvi.dwMinorVersion),
		Build: int(osvi.dwBuildNumber),
	}, nil
}

// 将版本号映射到版本字符串
func mapVersionToString(version *OSVersionInfo) string {
	// Windows 10 和 11 都是 Major=10, Minor=0，通过Build区分
	if version.Major == 10 && version.Minor == 0 {
		switch {
		case version.Build >= 22000: // Windows 11
			if version.Build >= 22621 {
				return "win11_22H2"
			}
			return "win11_21H2"
		case version.Build >= 19044: // Windows 10 21H2
			return "win10_21H2"
		case version.Build >= 19043: // Windows 10 21H1
			return "win10_21H1"
		case version.Build >= 19042: // Windows 10 20H2
			return "win10_20H2"
		case version.Build >= 19041: // Windows 10 2004
			return "win10_2004"
		case version.Build >= 18363: // Windows 10 1909
			return "win10_1909"
		case version.Build >= 18362: // Windows 10 1903
			return "win10_1903"
		default:
			return "win10_1903" // 默认到较早的Windows 10版本
		}
	}

	// 其他版本使用默认
	return "default"
}

// 根据函数名获取系统调用号
func GetSyscallNumber(functionName string) (uint32, error) {
	mapping, exists := syscallMappings[functionName]
	if !exists {
		return 0, fmt.Errorf("unknown syscall function: %s", functionName)
	}

	version := getCurrentWindowsVersion()
	syscallNum, exists := mapping.Numbers[version]
	if !exists {
		// 回退到默认版本
		syscallNum, exists = mapping.Numbers["default"]
		if !exists {
			return 0, fmt.Errorf("no syscall number found for %s", functionName)
		}
	}

	return syscallNum, nil
}

// 获取所有支持的系统调用函数名
func GetSupportedSyscalls() []string {
	var names []string
	for name := range syscallMappings {
		names = append(names, name)
	}
	return names
}

// 检查是否支持指定的系统调用
func IsSyscallSupported(functionName string) bool {
	_, exists := syscallMappings[functionName]
	return exists
}

// 添加新的系统调用映射
func AddSyscallMapping(name string, mapping *SyscallMapping) {
	syscallMappings[name] = mapping
}

// 根据部分名称搜索系统调用
func SearchSyscalls(partialName string) []string {
	var matches []string
	partialName = strings.ToLower(partialName)
	
	for name := range syscallMappings {
		if strings.Contains(strings.ToLower(name), partialName) {
			matches = append(matches, name)
		}
	}
	
	return matches
}
