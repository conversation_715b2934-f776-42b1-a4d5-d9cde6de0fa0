package main

import (
	"fmt"
	"strings"
)

// 计算字符串哈希（与api_hashing.go中相同）
func calculateHash(s string) uint32 {
	// 转换为小写以确保一致性
	s = strings.ToLower(s)
	
	// 使用简单的哈希算法
	hash := uint32(0)
	for _, c := range []byte(s) {
		hash = hash*31 + uint32(c)
	}
	return hash
}

func main() {
	fmt.Println("🔄 重新生成API哈希值")
	fmt.Println("====================")
	
	apis := []string{
		// kernel32.dll
		"VirtualAlloc", "VirtualProtect", "CreateThread", "WaitForSingleObject",
		"WriteProcessMemory", "VirtualProtectEx", "LoadLibraryA", "GetProcAddress",
		"GetTickCount", "CloseHandle", "GetLastInputInfo", "GetFileAttributesW",
		"GetDiskFreeSpaceExW", "GlobalMemoryStatusEx", "IsDebuggerPresent",
		"CheckRemoteDebuggerPresent", "ReadProcessMemory", "VirtualAllocEx",
		"ResumeThread", "OpenProcess", "VirtualQuery", "VirtualFree",
		"GlobalAddAtomA", "GlobalDeleteAtom", "GlobalGetAtomNameA",
		"CreateRemoteThread", "FlushInstructionCache", "GetSystemInfo",

		// ntdll.dll
		"EtwEventWrite", "RtlCopyMemory", "EtwpCreateEtwThread",
		"NtUnmapViewOfSection", "NtWriteVirtualMemory", "NtQueryInformationProcess",

		// user32.dll
		"EnumWindows", "GetCursorPos",

		// psapi.dll
		"EnumProcessModules", "GetModuleBaseNameA",

		// amsi.dll
		"AmsiScanBuffer",
	}
	
	dlls := []string{"kernel32.dll", "ntdll.dll", "user32.dll", "psapi.dll", "amsi.dll"}
	
	fmt.Println("// 预计算的API哈希值（更新后）")
	fmt.Println("var apiHashes = map[uint32]string{")
	for _, api := range apis {
		hash := calculateHash(api)
		fmt.Printf("\t0x%08x: \"%s\",\n", hash, api)
	}
	fmt.Println("}")
	
	fmt.Println("\n// DLL哈希值（更新后）")
	fmt.Println("var dllHashes = map[uint32]string{")
	for _, dll := range dlls {
		hash := calculateHash(dll)
		fmt.Printf("\t0x%08x: \"%s\",\n", hash, dll)
	}
	fmt.Println("}")
	
	// 特别显示GetTickCount的哈希
	fmt.Printf("\n特别注意：GetTickCount的新哈希值是: 0x%08x\n", calculateHash("GetTickCount"))
}
